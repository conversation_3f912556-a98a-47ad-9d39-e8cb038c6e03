import { useNavigate } from "react-router-dom";
import { useEventStore } from "../../lib/store/event";
import { useUserAuthStore } from "../../lib/store/auth";
import { useEffect } from "react";
import { EventCard } from "./event-card";
import { Head } from "../../components/reuseables/head";

export const EventSelection = () => {
  const navigate = useNavigate();
  const { userEvents, setSelectedEvent } = useEventStore();
  const { userData } = useUserAuthStore();

  useEffect(() => {
    if (userEvents.length === 0) {
      // First-timer: redirect to prelaunch introduction
      navigate("/prelaunch");
      return;
    }

    if (userEvents.length === 1) {
      setSelectedEvent(userEvents[0]);
      navigate("/");
      return;
    }
  }, [userEvents, navigate, setSelectedEvent]);

  const handleEventSelect = (eventId: string) => {
    const selectedEvent = userEvents.find((event) => event.id === eventId);
    if (selectedEvent) {
      setSelectedEvent(selectedEvent);
      navigate("/");
    }
  };

  if (userEvents.length === 0) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <div className="max-w-[561px] mx-auto px-4 py-6">
        <Head />

        <div className="mt-10 mb-8">
          <p className="text-[#666666] text-sm mb-4 uppercase tracking-[0.12em]">
            Hi {userData?.first_name}, Welcome Back!
          </p>
          <h1 className="text-[40px] font-bold leading-tight">
            What event would you
            <br />
            <span className="text-[#666666]"> like to manage today?</span>
          </h1>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
          {userEvents.map((event) => (
            <EventCard
              key={event.id}
              event={event}
              onSelect={handleEventSelect}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
