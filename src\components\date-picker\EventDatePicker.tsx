import { motion, AnimatePresence } from 'framer-motion';
import { CloseCircle } from 'iconsax-react';
import { DayPicker, DateRange } from 'react-day-picker';
import 'react-day-picker/dist/style.css';

interface EventDatePickerProps {
  isOpen: boolean;
  onClose: () => void;
  dateRange: DateRange | undefined;
  onDateRangeSelect: (range: DateRange | undefined) => void;
}

export const EventDatePicker: React.FC<EventDatePickerProps> = ({
  isOpen,
  onClose,
  dateRange,
  onDateRangeSelect,
}) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 z-50  flex items-center justify-center bg-black/40 backdrop-blur-sm"
          onClick={onClose}>
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className="mx-4 max-h-[90vh] w-full max-w-[522px] z-40 overflow-y-auto rounded-2xl bg-white shadow-xl sm:mx-0"
            onClick={(e) => e.stopPropagation()}>
            <div className="text-center flex-1 pr-6 mt-8">
              <h3 className="text-[28px] font-medium text-dark-200">
                Event Date
              </h3>
              <p className="mt-1 text-base text-grey-250">
                Please choose the date(s) for your event
              </p>
            </div>
            <button
              onClick={onClose}
              className="absolute top-4 right-4 transition-colors">
              <CloseCircle size="33" color="#4D55F2" variant="Bulk" />
            </button>
            <div className="p-6 flex justify-center">
              <style>
                {`
                  .rdp-caption_label {
                    color: #000;
                    font-weight: 600;
                    text-align: center;
                    font-size: 16px;
                    font-style: italic;
                  }
                  .rdp-day_range_middle {
                    background-color: rgba(77, 85, 242, 0.1);
                  }
                  .rdp {
                    margin: 0;
                    display: flex;
                    justify-content: center;
                  }
                  .rdp-months {
                    display: flex;
                    justify-content: center;
                  }
                  .rdp-month {
                    margin: 0;
                  }
                  .rdp-table {
                    margin: 0 auto;
                  }
                  .rdp-caption {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-bottom: 1rem;
                  }
                  .rdp-nav {
                    display: flex;
                    align-items: center;
                  }
                `}
              </style>
              <DayPicker
                mode="range"
                selected={dateRange}
                onSelect={onDateRangeSelect}
                disabled={{ before: new Date() }}
                startMonth={new Date()}
                className="w-full flex justify-center"
              />
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
