import { useUserAuthStore } from '../../lib/store/auth';
import logo from '../../assets/icons/Ep-Logo.svg';
import { Link } from 'react-router-dom';

export const Head = () => {
  const { userData } = useUserAuthStore();

  return (
    <div className="flex justify-between items-center max-w-[550px] mx-auto w-full h-[77px] ">
      <Link to="/" className="drop-shadow-xs">
        <img src={logo} alt="logo" className="drop-shadow-xs" />
      </Link>
      <button className="h-12 w-12 bg-white rounded-full flex justify-center items-center cursor-pointer shadow-xs">
        <span className="text-sm text-primary-650 font-bold bg-cus-pink-600 h-8 w-8 flex items-center justify-center rounded-full">
          {userData?.first_name?.charAt(0).toUpperCase() || 'T'}
          {userData?.last_name?.charAt(0).toUpperCase() || 'S'}
        </span>
      </button>
    </div>
  );
};
