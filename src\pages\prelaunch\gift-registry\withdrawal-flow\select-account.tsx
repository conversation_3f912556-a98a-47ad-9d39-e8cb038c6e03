import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  StepProgress,
  Step,
} from "../../../../components/step-progress/step-progress";
import { Footer } from "../../footer";
import {
  WalletAPI,
  Wallet,
  PayoutAccount,
  PayoutBank,
  CreatePayoutAccountPayload,
} from "../../../../lib/apis/walletapi";
import { useUserAuthStore } from "../../../../lib/store/auth";
import { toast } from "react-toastify";

// Bank illustration SVG components
const BankIllustrationDefault: React.FC = () => (
  <svg
    width="98"
    height="86"
    viewBox="0 0 98 86"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path opacity="0.6" d="M40 55H20V90H40V55Z" fill="#EDEEFE" />
    <path opacity="0.4" d="M60 55H40V90H60V55Z" fill="#EDEEFE" />
    <path opacity="0.6" d="M80 55H60V90H80V55Z" fill="#EDEEFE" />
    <path opacity="0.4" d="M100 55H80V90H100V55Z" fill="#EDEEFE" />
    <path
      d="M106.85 28.7491L61.85 10.7492C60.85 10.3492 59.15 10.3492 58.15 10.7492L13.15 28.7491C11.4 29.4491 10 31.4991 10 33.3991V49.9991C10 52.7491 12.25 54.9991 15 54.9991H105C107.75 54.9991 110 52.7491 110 49.9991V33.3991C110 31.4991 108.6 29.4491 106.85 28.7491ZM60 42.4991C55.85 42.4991 52.5 39.1491 52.5 34.9991C52.5 30.8491 55.85 27.4991 60 27.4991C64.15 27.4991 67.5 30.8491 67.5 34.9991C67.5 39.1491 64.15 42.4991 60 42.4991Z"
      fill="#EDEEFE"
    />
  </svg>
);

const BankIllustrationSelected: React.FC = () => (
  <svg
    width="98"
    height="86"
    viewBox="0 0 98 86"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path opacity="0.6" d="M40 55H20V90H40V55Z" fill="#A6AAF9" />
    <path opacity="0.4" d="M60 55H40V90H60V55Z" fill="#A6AAF9" />
    <path opacity="0.6" d="M80 55H60V90H80V55Z" fill="#A6AAF9" />
    <path opacity="0.4" d="M100 55H80V90H100V55Z" fill="#A6AAF9" />
    <path
      d="M106.85 28.7491L61.85 10.7492C60.85 10.3492 59.15 10.3492 58.15 10.7492L13.15 28.7491C11.4 29.4491 10 31.4991 10 33.3991V49.9991C10 52.7491 12.25 54.9991 15 54.9991H105C107.75 54.9991 110 52.7491 110 49.9991V33.3991C110 31.4991 108.6 29.4491 106.85 28.7491ZM60 42.4991C55.85 42.4991 52.5 39.1491 52.5 34.9991C52.5 30.8491 55.85 27.4991 60 27.4991C64.15 27.4991 67.5 30.8491 67.5 34.9991C67.5 39.1491 64.15 42.4991 60 42.4991Z"
      fill="#A6AAF9"
    />
  </svg>
);

export const SelectAccount: React.FC = () => {
  const navigate = useNavigate();
  const { userData } = useUserAuthStore();

  // State management
  const [selectedAccount, setSelectedAccount] = useState<PayoutAccount | null>(
    null
  );
  const [showAddAccount, setShowAddAccount] = useState<boolean>(false);
  const [bankName, setBankName] = useState<string>("");
  const [accountNumber, setAccountNumber] = useState<string>("");
  const [accountName, setAccountName] = useState<string>("");
  console.log(accountName);
  const [showCreatePin, setShowCreatePin] = useState<boolean>(false);
  const [showBankDropdown, setShowBankDropdown] = useState<boolean>(false);
  const [bankSearchQuery, setBankSearchQuery] = useState<string>("");
  const [showPinSuccess, setShowPinSuccess] = useState<boolean>(false);
  const bankDropdownRef = useRef<HTMLDivElement>(null);
  const [newPin, setNewPin] = useState<string[]>(["", "", "", ""]);
  const [loading, setLoading] = useState<boolean>(true);
  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [payoutAccounts, setPayoutAccounts] = useState<PayoutAccount[]>([]);
  const [banks, setBanks] = useState<PayoutBank[]>([]);
  const [isCreatingAccount, setIsCreatingAccount] = useState<boolean>(false);
  const [isSettingPin, setIsSettingPin] = useState<boolean>(false);
  const [isResolvingAccount, setIsResolvingAccount] = useState<boolean>(false);
  console.log(isSettingPin, isCreatingAccount);

  // Initialize component on mount
  useEffect(() => {
    initializeWithdrawal();
  }, []);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        bankDropdownRef.current &&
        !bankDropdownRef.current.contains(event.target as Node)
      ) {
        setShowBankDropdown(false);
      }
    };

    if (showBankDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showBankDropdown]);

  // Account resolution effect
  useEffect(() => {
    if (bankName && accountNumber && accountNumber.length === 10) {
      setIsResolvingAccount(true);
      setAccountName("");

      const resolveAccount = async () => {
        try {
          const selectedBank = banks.find(
            (bank) => bank.bank_name === bankName
          );
          if (!selectedBank) {
            setAccountName("");
            setIsResolvingAccount(false);
            return;
          }

          const result = await WalletAPI.resolvePayoutAccount({
            account_number: accountNumber,
            bank_code: selectedBank.bank_code,
            currency_code: "ngn",
          });
          setAccountName(result.account_name);
        } catch (error) {
          console.error("Account resolution failed:", error);
          setAccountName("");
          toast.error("Unable to verify account details");
        } finally {
          setIsResolvingAccount(false);
        }
      };

      // Debounce the API call
      const timer = setTimeout(resolveAccount, 800);
      return () => clearTimeout(timer);
    } else {
      setAccountName("");
      setIsResolvingAccount(false);
    }
  }, [bankName, accountNumber, banks]);

  const initializeWithdrawal = async () => {
    try {
      setLoading(true);

      // Check if user has wallet
      const wallets = await WalletAPI.getUserWallets();
      if (wallets.length === 0) {
        toast.error("You need to create a wallet first");
        navigate("/wallet"); // Redirect to wallet creation
        return;
      }

      const userWallet = wallets[0];
      setWallet(userWallet);

      // Check if user has password set - if not, show PIN creation
      if (!userData?.transaction_pin_set) {
        setShowCreatePin(true);
      }

      // Load payout accounts and banks
      const [accounts, bankList] = await Promise.all([
        WalletAPI.getPayoutAccounts(),
        WalletAPI.getPayoutBanks("ngn"),
      ]);

      setPayoutAccounts(accounts);
      setBanks(bankList);
    } catch (error) {
      console.error("Failed to initialize withdrawal:", error);
      toast.error("Failed to initialize withdrawal");
      navigate("/");
    } finally {
      setLoading(false);
    }
  };

  const steps: Step[] = showCreatePin
    ? [
        { id: 1, name: "Create Transaction Pin" },
        { id: 2, name: "Select Account" },
        { id: 3, name: "Authenticate" },
      ]
    : [
        { id: 1, name: "Select Account" },
        { id: 2, name: "Authenticate" },
      ];

  useEffect(() => {
    const scrollY = window.scrollY;
    document.body.style.position = "fixed";
    document.body.style.top = `-${scrollY}px`;
    document.body.style.width = "100%";
    document.body.style.overflow = "hidden";
    return () => {
      const scrollY = document.body.style.top;
      document.body.style.position = "";
      document.body.style.top = "";
      document.body.style.width = "";
      document.body.style.overflow = "";
      window.scrollTo(0, parseInt(scrollY || "0") * -1);
    };
  }, []);

  const handleCancel = () => {
    navigate(-1);
  };

  const handleContinue = () => {
    if (selectedAccount) {
      // Pass selected account data to withdrawal amount page
      navigate("/withdrawal/amount", {
        state: {
          selectedAccount,
          wallet,
        },
      });
    }
  };

  const handleAccountSelection = (account: PayoutAccount) => {
    setSelectedAccount(account);
  };

  const handleAddAccount = () => {
    setShowAddAccount(true);
  };

  const handleBackToSelect = () => {
    setShowAddAccount(false);
    setBankName("");
    setAccountNumber("");
    setAccountName("");
    setShowBankDropdown(false);
    setBankSearchQuery("");
    setIsResolvingAccount(false);
  };

  // Filter banks based on search query
  const filteredBanks = banks.filter((bank) =>
    bank.bank_name.toLowerCase().includes(bankSearchQuery.toLowerCase())
  );

  // Handle bank selection
  const handleBankSelect = (bank: PayoutBank) => {
    setBankName(bank.bank_name);
    setShowBankDropdown(false);
    setBankSearchQuery("");
  };

  const handleAddAccountSubmit = async () => {
    if (!bankName || accountNumber.length !== 10) return;

    try {
      setIsCreatingAccount(true);
      const selectedBank = banks.find((bank) => bank.bank_name === bankName);
      if (!selectedBank) {
        toast.error("Please select a valid bank");
        return;
      }

      const payload: CreatePayoutAccountPayload = {
        account_number: accountNumber,
        bank_code: selectedBank.bank_code,
        currency_code: "ngn",
      };

      const newAccount = await WalletAPI.createPayoutAccount(payload);
      setPayoutAccounts((prev) => [...prev, newAccount]);
      setSelectedAccount(newAccount);
      toast.success("Account added successfully!");
      handleBackToSelect();
    } catch (error) {
      console.error("Failed to add account:", error);
      toast.error("Failed to add account");
    } finally {
      setIsCreatingAccount(false);
    }
  };

  const handlePinInputChange = (index: number, value: string) => {
    if ((value as string).length <= 1 && /^\d*$/.test(value as string)) {
      const newPinArray = [...newPin];
      newPinArray[index] = value as string;
      setNewPin(newPinArray);

      // Auto-focus next input
      if (value && index < 3) {
        const nextInput = document.getElementById(`pin-input-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleSetPin = async () => {
    const pinString: string = newPin.join("");
    if (pinString.length === 4 && wallet) {
      try {
        setIsSettingPin(true);
        await WalletAPI.setWalletPin(wallet.id, { pin: pinString });
        setShowCreatePin(false);
        setShowPinSuccess(true);
        toast.success("Transaction PIN set successfully!");
      } catch (error) {
        console.error("Failed to set PIN:", error);
        toast.error("Failed to set transaction PIN");
      } finally {
        setIsSettingPin(false);
      }
    }
  };

  const handlePinSuccessClose = () => {
    setShowPinSuccess(false);
    // Reset to show select account
  };

  if (loading) {
    return (
      <div className="fixed inset-0 z-10 overflow-y-auto bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4" />
          <p className="text-gray-600">Loading withdrawal options...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-10 overflow-y-auto bg-white">
      <div
        style={{
          background: "linear-gradient(180deg, #FEFAF8 0%, #F5F6FE 100%)",
        }}
        className="flex flex-col w-full font-rethink min-h-screen sm:pb-[100px] "
      >
        {showAddAccount ? (
          // Add New Account Full-Screen Panel (Pixel-perfect Figma, Responsive)
          <div
            className="min-h-screen w-full flex items-center justify-center"
            style={{
              background: "linear-gradient(180deg, #FEFAF8 0%, #F5F6FE 100%)",
            }}
          >
            <div className="relative w-[96%] sm:w-full max-w-[696px] rounded-2xl mt-10 shadow-[0_12px_120px_0_rgba(95,95,95,0.06)] bg-white px-0 py-0 flex flex-col min-h-[805px]  mx-auto">
              {/* Back Button */}
              <div className="absolute top-[-100px] sm:top-[-100px]">
                <button
                  onClick={handleBackToSelect}
                  className="absolute min-w-max top-8 left-0  flex items-center gap-2 text-primary font-medium text-base focus:outline-none z-10 bg-white rounded-full px-4 py-[10px] shadow"
                  aria-label="Back to Withdrawal"
                  style={{
                    boxShadow: "0px 1px 2px 0px rgba(10, 13, 18, 0.05)",
                  }}
                >
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      opacity="0.4"
                      d="M9.9974 18.3346C14.5998 18.3346 18.3307 14.6037 18.3307 10.0013C18.3307 5.39893 14.5998 1.66797 9.9974 1.66797C5.39502 1.66797 1.66406 5.39893 1.66406 10.0013C1.66406 14.6037 5.39502 18.3346 9.9974 18.3346Z"
                      fill="#4D55F2"
                    />
                    <path
                      d="M12.9172 9.37682H8.59219L10.0255 7.94349C10.2672 7.70182 10.2672 7.30182 10.0255 7.06016C9.78385 6.81849 9.38385 6.81849 9.14219 7.06016L6.64219 9.56016C6.40052 9.80182 6.40052 10.2018 6.64219 10.4435L9.14219 12.9435C9.26719 13.0685 9.42552 13.1268 9.58385 13.1268C9.74219 13.1268 9.90052 13.0685 10.0255 12.9435C10.2672 12.7018 10.2672 12.3018 10.0255 12.0602L8.59219 10.6268H12.9172C13.2589 10.6268 13.5422 10.3435 13.5422 10.0018C13.5422 9.66016 13.2589 9.37682 12.9172 9.37682Z"
                      fill="#4D55F2"
                    />
                  </svg>

                  <span>Back to Withdrawal</span>
                </button>
              </div>

              {/* Info Box */}
              <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-4 bg-[#FEFAF8] border border-[#FEF5F1] rounded-2xl px-4 sm:px-6 py-3 sm:py-4 mt-20 sm:mt-[100px] mx-4 sm:mx-8 mb-6 sm:mb-8">
                <div className="flex-shrink-0">
                  {/* Bank Icon */}
                  <svg
                    width="58"
                    height="58"
                    viewBox="0 0 58 58"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M52.8994 44.5119L53.1437 51.5076L6.50542 53.1362L6.26112 46.1405C6.21633 44.858 7.22905 43.7719 8.5116 43.7272L50.486 42.2614C51.7686 42.2166 52.8546 43.2293 52.8994 44.5119Z"
                      fill="url(#paint0_linear_12635_63748)"
                      stroke="url(#paint1_linear_12635_63748)"
                      stroke-width="1.5"
                      stroke-miterlimit="10"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      opacity="0.6"
                      d="M19.6011 26.9946L10.2734 27.3203L10.8435 43.6437L20.1711 43.318L19.6011 26.9946Z"
                      fill="url(#paint2_linear_12635_63748)"
                    />
                    <path
                      opacity="0.4"
                      d="M28.9292 26.6704L19.6016 26.9961L20.1716 43.3195L29.4992 42.9937L28.9292 26.6704Z"
                      fill="url(#paint3_linear_12635_63748)"
                    />
                    <path
                      opacity="0.6"
                      d="M38.2495 26.3461L28.9219 26.6719L29.4919 42.9953L38.8195 42.6695L38.2495 26.3461Z"
                      fill="url(#paint4_linear_12635_63748)"
                    />
                    <path
                      opacity="0.4"
                      d="M47.5777 26.018L38.25 26.3438L38.82 42.6671L48.1477 42.3414L47.5777 26.018Z"
                      fill="url(#paint5_linear_12635_63748)"
                    />
                    <path
                      d="M55.5325 53.1726L4.23046 54.9641C3.27437 54.9975 2.45384 54.2324 2.42045 53.2763C2.38706 52.3202 3.15223 51.4997 4.10831 51.4663L55.4104 49.6748C56.3665 49.6414 57.187 50.4065 57.2204 51.3626C57.2538 52.3187 56.4886 53.1392 55.5325 53.1726Z"
                      fill="url(#paint6_linear_12635_63748)"
                    />
                    <path
                      d="M50.35 13.6644L29.0696 6.00249C28.5967 5.83223 27.8039 5.85991 27.344 6.06275L6.64997 15.1905C5.8452 15.5454 5.22564 16.5243 5.25658 17.4104L5.52694 25.1524C5.57173 26.4349 6.65773 27.4477 7.94028 27.4029L49.9147 25.9371C51.1972 25.8923 52.21 24.8063 52.1652 23.5238L51.8948 15.7818C51.8639 14.8957 51.1775 13.9624 50.35 13.6644ZM28.7239 20.8402C26.7884 20.9078 25.1715 19.4 25.1039 17.4645C25.0363 15.529 26.5441 13.9121 28.4796 13.8445C30.4151 13.7769 32.032 15.2847 32.0996 17.2202C32.1672 19.1557 30.6594 20.7726 28.7239 20.8402Z"
                      fill="url(#paint7_linear_12635_63748)"
                    />
                    <defs>
                      <linearGradient
                        id="paint0_linear_12635_63748"
                        x1="29.4988"
                        y1="42.9943"
                        x2="29.8245"
                        y2="52.3219"
                        gradientUnits="userSpaceOnUse"
                      >
                        <stop stop-color="#FCE8DF" />
                        <stop offset="1" stop-color="#FFAA8C" />
                      </linearGradient>
                      <linearGradient
                        id="paint1_linear_12635_63748"
                        x1="29.4988"
                        y1="42.9943"
                        x2="29.8245"
                        y2="52.3219"
                        gradientUnits="userSpaceOnUse"
                      >
                        <stop stop-color="#FCE8DF" />
                        <stop offset="1" stop-color="#FFAA8C" />
                      </linearGradient>
                      <linearGradient
                        id="paint2_linear_12635_63748"
                        x1="14.9373"
                        y1="27.1574"
                        x2="15.5073"
                        y2="43.4808"
                        gradientUnits="userSpaceOnUse"
                      >
                        <stop stop-color="#FCE8DF" />
                        <stop offset="1" stop-color="#FFAA8C" />
                      </linearGradient>
                      <linearGradient
                        id="paint3_linear_12635_63748"
                        x1="24.2654"
                        y1="26.8332"
                        x2="24.8354"
                        y2="43.1566"
                        gradientUnits="userSpaceOnUse"
                      >
                        <stop stop-color="#FCE8DF" />
                        <stop offset="1" stop-color="#FFAA8C" />
                      </linearGradient>
                      <linearGradient
                        id="paint4_linear_12635_63748"
                        x1="33.5857"
                        y1="26.509"
                        x2="34.1557"
                        y2="42.8324"
                        gradientUnits="userSpaceOnUse"
                      >
                        <stop stop-color="#FCE8DF" />
                        <stop offset="1" stop-color="#FFAA8C" />
                      </linearGradient>
                      <linearGradient
                        id="paint5_linear_12635_63748"
                        x1="42.9138"
                        y1="26.1809"
                        x2="43.4839"
                        y2="42.5043"
                        gradientUnits="userSpaceOnUse"
                      >
                        <stop stop-color="#FCE8DF" />
                        <stop offset="1" stop-color="#FFAA8C" />
                      </linearGradient>
                      <linearGradient
                        id="paint6_linear_12635_63748"
                        x1="29.7593"
                        y1="50.5705"
                        x2="29.8815"
                        y2="54.0684"
                        gradientUnits="userSpaceOnUse"
                      >
                        <stop stop-color="#FCE8DF" />
                        <stop offset="1" stop-color="#FFAA8C" />
                      </linearGradient>
                      <linearGradient
                        id="paint7_linear_12635_63748"
                        x1="28.2019"
                        y1="5.89271"
                        x2="28.9275"
                        y2="26.67"
                        gradientUnits="userSpaceOnUse"
                      >
                        <stop stop-color="#FCE8DF" />
                        <stop offset="1" stop-color="#FFAA8C" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
                <p className="text-xs sm:text-sm text-[#000030] italic font-medium">
                  Add a new bank account to withdraw your funds. Please ensure
                  the account name matches the name on your profile.
                </p>
              </div>

              {/* Add Account Form */}
              <form
                className="flex flex-col gap-4 sm:gap-6 px-4 sm:px-8 pb-8"
                onSubmit={(e) => {
                  e.preventDefault();
                  handleAddAccountSubmit();
                }}
                aria-label="Add New Account Form"
              >
                {/* Bank Selection */}
                <div>
                  <label
                    htmlFor="bank-select"
                    className="block text-sm font-medium text-grey-50 mb-2"
                  >
                    Bank
                  </label>
                  <div className="relative" ref={bankDropdownRef}>
                    {/* Custom Bank Select */}
                    <button
                      type="button"
                      onClick={() => setShowBankDropdown(!showBankDropdown)}
                      className="w-full h-12 px-4 border border-[#D5D7DA] rounded-full text-base font-normal outline-0 focus:border-primary-50 bg-white text-left flex items-center justify-between"
                    >
                      <span
                        className={bankName ? "text-black" : "text-gray-500"}
                      >
                        {bankName || "Select Bank"}
                      </span>
                      <svg
                        className={`transition-transform duration-200 ${
                          showBankDropdown ? "rotate-180" : ""
                        }`}
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M5 8L10 13L15 8"
                          stroke="#6B7280"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </button>

                    {/* Dropdown */}
                    {showBankDropdown && (
                      <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-[#D5D7DA] rounded-2xl shadow-lg z-50 max-h-80 overflow-hidden">
                        {/* Search Input */}
                        <div className="p-3 border-b border-[#E5E7EB]">
                          <div className="relative">
                            <svg
                              className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
                              width="16"
                              height="16"
                              viewBox="0 0 16 16"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M7.33333 12.6667C10.2789 12.6667 12.6667 10.2789 12.6667 7.33333C12.6667 4.38781 10.2789 2 7.33333 2C4.38781 2 2 4.38781 2 7.33333C2 10.2789 4.38781 12.6667 7.33333 12.6667Z"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M14 14L11.1 11.1"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                            <input
                              type="text"
                              placeholder="Search bank name..."
                              value={bankSearchQuery}
                              onChange={(e) =>
                                setBankSearchQuery(e.target.value)
                              }
                              className="w-full pl-10 pr-4 py-2 border border-transparent rounded-full text-sm outline-0 focus:border-primary-50"
                              autoFocus
                            />
                          </div>
                        </div>

                        {/* Bank List */}
                        <div className="max-h-60 overflow-y-auto">
                          {filteredBanks.length > 0 ? (
                            filteredBanks.map((bank) => (
                              <button
                                key={bank.bank_code}
                                type="button"
                                onClick={() => handleBankSelect(bank)}
                                className="w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors border-b border-gray-100 last:border-b-0"
                              >
                                <div className="font-medium text-gray-900">
                                  {bank.bank_name}
                                </div>
                              </button>
                            ))
                          ) : (
                            <div className="px-4 py-6 text-center text-gray-500">
                              No banks found matching "{bankSearchQuery}"
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Account Number */}
                <div>
                  <label
                    htmlFor="account-number"
                    className="block text-sm font-medium text-grey-50 mb-2"
                  >
                    Account Number
                  </label>
                  <input
                    id="account-number"
                    type="text"
                    value={accountNumber}
                    onChange={(e) => setAccountNumber(e.target.value)}
                    placeholder="**********"
                    className="w-full h-12 px-4 border border-[#D5D7DA] rounded-full text-base font-normal outline-0 focus:border-primary-50"
                    maxLength={10}
                    inputMode="numeric"
                    pattern="\d{10}"
                    required
                    aria-describedby="account-number-hint"
                  />
                  <p
                    id="account-number-hint"
                    className="text-xs text-grey-250 mt-1"
                  >
                    Enter account number to receive funds
                  </p>
                </div>

                {/* Account Name Display */}
                {accountNumber.length === 10 && (
                  <div className="mt-2">
                    {isResolvingAccount ? (
                      <div className="flex items-center gap-2 text-blue-600 bg-[#EFF6FF] rounded-full px-4 py-2 w-fit">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                        <span className="text-sm font-medium">
                          Verifying account...
                        </span>
                      </div>
                    ) : accountName ? (
                      <div className="flex items-center gap-2 text-green-600 bg-[#ECFDF3] rounded-full px-4 py-2 w-fit">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M13.3333 4L6 11.3333L2.66667 8"
                            stroke="#10B981"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        <span className="text-sm font-bold">{accountName}</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-red-600 bg-[#FEF2F2] rounded-full px-4 py-2 w-fit">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8 1.33333C4.32 1.33333 1.33333 4.32 1.33333 8C1.33333 11.68 4.32 14.6667 8 14.6667C11.68 14.6667 14.6667 11.68 14.6667 8C14.6667 4.32 11.68 1.33333 8 1.33333ZM10.6667 10.6667L8 8L5.33333 10.6667L5.33333 5.33333L8 8L10.6667 5.33333V10.6667Z"
                            fill="#DC2626"
                          />
                        </svg>
                        <span className="text-sm font-medium">
                          Account verification failed
                        </span>
                      </div>
                    )}
                  </div>
                )}

                {/* Continue Button */}
                <button
                  type="submit"
                  disabled={
                    !bankName ||
                    accountNumber.length !== 10 ||
                    !accountName ||
                    isResolvingAccount
                  }
                  className={`w-full py-3 sm:py-4 px-4 sm:px-6 flex gap-2 items-center justify-center rounded-full font-bold text-base sm:text-lg transition-all mt-6 sm:mt-8 ${
                    bankName &&
                    accountNumber.length === 10 &&
                    accountName &&
                    !isResolvingAccount
                      ? "bg-primary text-white hover:bg-primary/90"
                      : "bg-primary/50 text-white cursor-not-allowed"
                  }`}
                >
                  <span>Continue</span>
                  <svg
                    width="21"
                    height="20"
                    viewBox="0 0 21 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      opacity="0.4"
                      d="M10.4974 18.3327C15.0998 18.3327 18.8307 14.6017 18.8307 9.99935C18.8307 5.39698 15.0998 1.66602 10.4974 1.66602C5.89502 1.66602 2.16406 5.39698 2.16406 9.99935C2.16406 14.6017 5.89502 18.3327 10.4974 18.3327Z"
                      fill="white"
                    />
                    <path
                      d="M13.8609 9.5582L11.3609 7.0582C11.1193 6.81654 10.7193 6.81654 10.4776 7.0582C10.2359 7.29987 10.2359 7.69987 10.4776 7.94154L11.9109 9.37487H7.58594C7.24427 9.37487 6.96094 9.6582 6.96094 9.99987C6.96094 10.3415 7.24427 10.6249 7.58594 10.6249H11.9109L10.4776 12.0582C10.2359 12.2999 10.2359 12.6999 10.4776 12.9415C10.6026 13.0665 10.7609 13.1249 10.9193 13.1249C11.0776 13.1249 11.2359 13.0665 11.3609 12.9415L13.8609 10.4415C14.1026 10.1999 14.1026 9.79987 13.8609 9.5582Z"
                      fill="white"
                    />
                  </svg>
                </button>
              </form>
            </div>
          </div>
        ) : (
          // Main Content (step progress, select account, etc.)
          <div className="w-full min-h-screen flex flex-col">
            <div className="fixed top-0 left-0 px-2 sm:px-4 md:px-0 right-0 z-50 max-w-full sm:max-w-[560px] w-full mx-auto">
              <div className="h-[48px] absolute top-[-50px] bg-transparent w-full max-w-[443px] blur-xl [box-shadow:0px_33.75px_33.75px_0px_#A6A6A60A,0px_67.49px_84.37px_0px_#A6A6A61A,0px_39.68px_198.42px_0px_#0000000F] group-scroll:shadow-none group-scroll:bg-white/10 group-scroll:backdrop-blur-md"></div>
              <div className="">
                <div className="flex justify-end pt-8 pb-5 ">
                  <button
                    onClick={handleCancel}
                    className="px-4 sm:px-6 py-2 sm:py-3 cursor-pointer rounded-full bg-primary-250 text-primary font-medium text-sm sm:text-base mr-2 sm:mr-0"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>

            <div className="max-w-full sm:max-w-[560px] w-full mx-auto pt-24 sm:pt-32 px-2 sm:px-4 md:px-0 ">
              <h1 className="text-[22px] sm:text-[28px] font-semibold mt-7 sm:ml-3.5">
                Withdraw from Wallet
              </h1>
              <p className="text-base text-grey-950 sm:ml-3.5">
                Move your funds safely to your bank account.
              </p>
            </div>

            {/* Step Progress */}
            <div className="px-2 sm:px-0">
              <StepProgress
                steps={steps}
                activeStep={1}
                completedSteps={[]}
                onStepChange={() => {}}
              />
            </div>

            {/* Content */}
            <div className="px-2 sm:px-0 sm:ml-3.5">
              <div className="max-w-full sm:max-w-[560px] w-full mx-auto mt-8">
                {showCreatePin ? (
                  // Create PIN Flow
                  <div>
                    <div className="bg-[#FAF9FF] p-5 h-[84px]">
                      <p className="text-black italic font-medium mb-[40px] text-sm sm:text-base">
                        Create your preferred 4 digit transaction pin to
                        securely perform your transactions
                      </p>
                    </div>

                    {/* PIN Input Fields */}
                    <div className="flex justify-center mt-[48px] sm:mt-[64px] mb-[36px] sm:mb-[52px] gap-2 sm:gap-4 ">
                      {newPin.map((digit, index) => (
                        <div key={index} className="relative">
                          <input
                            id={`pin-input-${index}`}
                            type="text"
                            value={digit}
                            onChange={(e) =>
                              handlePinInputChange(index, e.target.value)
                            }
                            className="w-12 sm:w-16 h-12 sm:h-16 text-center text-xl sm:text-2xl font-bold border-2 border-[#D5D7DA] rounded-full focus:border-primary outline-none"
                            maxLength={1}
                          />
                        </div>
                      ))}
                    </div>

                    {/* Set PIN Button */}
                    <button
                      onClick={handleSetPin}
                      disabled={newPin.join("").length !== 4}
                      className={`w-full py-3 sm:py-4 px-4 sm:px-6 flex gap-2 items-center justify-center rounded-full font-bold text-base sm:text-lg transition-all ${
                        newPin.join("").length === 4
                          ? "bg-primary text-white hover:bg-primary/90"
                          : "bg-primary/50 text-white hover:bg-primary/90 cursor-not-allowed"
                      }`}
                    >
                      <span>Set Pin</span>
                    </button>
                  </div>
                ) : (
                  // Select Account View
                  <div>
                    <p className="text-black text-[13px] sm:text-[14px] italic font-medium mb-[32px] sm:mb-[40px]">
                      Select the account you would want your funds sent into
                    </p>

                    <div className="space-y-4 w-full sm:w-[400px] mb-8">
                      {/* Dynamic Account Cards from API */}
                      {payoutAccounts.map((account) => (
                        <div
                          key={account.id}
                          onClick={() => handleAccountSelection(account)}
                          className={`relative p-4 sm:p-6 border-2 h-[110px] sm:h-[130px] rounded-2xl cursor-pointer bg-[#FBF9FA] transition-all ${
                            selectedAccount?.id === account.id
                              ? "border-primary-50 bg-[#F5F6FE] "
                              : "border-[#FBF9FA] hover:border-primary-50"
                          }`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-2 sm:gap-4">
                              <div>
                                <p
                                  className={`font-bold text-[16px] sm:text-[20px] ${
                                    selectedAccount?.id === account.id
                                      ? "text-[#00008C]"
                                      : "text-grey-50"
                                  }`}
                                >
                                  {account.account_name}
                                </p>
                                <p className="text-xs sm:text-sm text-grey-250">
                                  {banks.find(
                                    (bank) =>
                                      bank.bank_code === account.bank_code
                                  )?.bank_name || account.bank_code}
                                </p>
                                <p className="text-xs sm:text-sm text-grey-250">
                                  {account.account_number}
                                </p>
                              </div>
                            </div>
                            {selectedAccount?.id === account.id && (
                              <svg
                                className="mt-[-14px]"
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <rect
                                  x="1"
                                  y="1"
                                  width="22"
                                  height="22"
                                  rx="11"
                                  fill="#343CD8"
                                />
                                <rect
                                  x="1"
                                  y="1"
                                  width="22"
                                  height="22"
                                  rx="11"
                                  stroke="#343CD8"
                                  strokeWidth="2"
                                />
                                <path
                                  d="M16 9L10.5 14.5L8 12"
                                  stroke="white"
                                  strokeWidth="1.4"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            )}
                            <div className="flex-shrink-0 absolute bottom-0 right-0">
                              {selectedAccount?.id === account.id ? (
                                <BankIllustrationSelected />
                              ) : (
                                <BankIllustrationDefault />
                              )}
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* Add New Account Card - NOT SELECTABLE */}
                      <div className="relative p-4 sm:p-6 border-2 border-[#A6AAF9] h-[110px] sm:h-[130px] rounded-2xl border-b-0 bg-[#F8F9FF] ">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-2 sm:gap-4">
                            <div>
                              <p className="font-bold text-grey-50 text-[16px] sm:text-[20px]">
                                Add New Withdrawal Account
                              </p>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleAddAccount();
                                }}
                                className="text-xs sm:text-sm text-[#FFFFFF] font-medium px-2 sm:px-3 py-1 rounded-full mt-2 bg-[#7177F5] border h-[32px] sm:h-[36px] transition-all w-fit items-center flex gap-2 min-w-max"
                              >
                                <span>Add new account</span>
                                <svg
                                  width="20"
                                  height="20"
                                  viewBox="0 0 20 20"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    opacity="0.4"
                                    d="M9.9974 18.3327C14.5998 18.3327 18.3307 14.6017 18.3307 9.99935C18.3307 5.39698 14.5998 1.66602 9.9974 1.66602C5.39502 1.66602 1.66406 5.39698 1.66406 9.99935C1.66406 14.6017 5.39502 18.3327 9.9974 18.3327Z"
                                    fill="white"
                                  />
                                  <path
                                    d="M13.3609 9.5582L10.8609 7.0582C10.6193 6.81654 10.2193 6.81654 9.9776 7.0582C9.73594 7.29987 9.73594 7.69987 9.9776 7.94154L11.4109 9.37487H7.08594C6.74427 9.37487 6.46094 9.6582 6.46094 9.99987C6.46094 10.3415 6.74427 10.6249 7.08594 10.6249H11.4109L9.9776 12.0582C9.73594 12.2999 9.73594 12.6999 9.9776 12.9415C10.1026 13.0665 10.2609 13.1249 10.4193 13.1249C10.5776 13.1249 10.7359 13.0665 10.8609 12.9415L13.3609 10.4415C13.6026 10.1999 13.6026 9.79987 13.3609 9.5582Z"
                                    fill="white"
                                  />
                                </svg>
                              </button>
                            </div>
                          </div>
                          <div className="flex-shrink-0 absolute bottom-0 right-0">
                            <svg
                              width="108"
                              height="96"
                              viewBox="0 0 108 96"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M50.4551 15.4551C57.11 8.84875 67.8957 8.84805 74.5508 15.4541L83.2979 24.3018L83.3027 24.3057C86.2554 27.2122 87.8886 30.859 88.2119 34.667C87.1672 34.5358 86.0949 34.5 85.0029 34.5H35.0029C33.9326 34.5 32.8895 34.5566 31.874 34.6719C32.4965 33.6472 33.2871 32.6738 34.207 31.7539V31.7529L50.4551 15.4551Z"
                                fill="#DBDDFC"
                                stroke="#DBDDFC"
                              />
                              <path
                                d="M25 75C13.95 75 5 83.95 5 95C5 98.75 6.05 102.3 7.9 105.3C11.35 111.1 17.7 115 25 115C32.3 115 38.65 111.1 42.1 105.3C43.95 102.3 45 98.75 45 95C45 83.95 36.05 75 25 75ZM32.45 98.6499H28.75V102.55C28.75 104.6 27.05 106.3 25 106.3C22.95 106.3 21.25 104.6 21.25 102.55V98.6499H17.55C15.5 98.6499 13.8 96.9499 13.8 94.8999C13.8 92.8499 15.5 91.1499 17.55 91.1499H21.25V87.6001C21.25 85.5501 22.95 83.8501 25 83.8501C27.05 83.8501 28.75 85.5501 28.75 87.6001V91.1499H32.45C34.5 91.1499 36.2 92.8499 36.2 94.8999C36.2 96.9499 34.55 98.6499 32.45 98.6499Z"
                                fill="#DBDDFC"
                              />
                              <path
                                opacity="0.4"
                                d="M110 60V85C110 100 100 110 85 110H38.15C39.7 108.7 41.05 107.1 42.1 105.3C43.95 102.3 45 98.75 45 95C45 83.95 36.05 75 25 75C19 75 13.65 77.6499 10 81.7999V60C10 46.4 18.2 36.9 30.95 35.3C32.25 35.1 33.6 35 35 35H85C86.3 35 87.55 35.0499 88.75 35.2499C101.65 36.7499 110 46.3 110 60Z"
                                fill="#DBDDFC"
                              />
                              <path
                                d="M110 62.5H95C89.5 62.5 85 67 85 72.5C85 78 89.5 82.5 95 82.5H110"
                                fill="#DBDDFC"
                              />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>

                    <button
                      onClick={handleContinue}
                      disabled={!selectedAccount}
                      className={`w-full sm:w-[400px] py-3 sm:py-4 px-4 sm:px-6 flex gap-2 items-center justify-center mt-10 sm:mt-[60px] rounded-full font-bold text-base sm:text-lg transition-all ${
                        selectedAccount
                          ? "bg-primary  text-white hover:bg-primary/90"
                          : "bg-primary/50  text-white hover:bg-primary/90/50 cursor-not-allowed"
                      }`}
                    >
                      <span> Continue</span>
                      <svg
                        width="21"
                        height="20"
                        viewBox="0 0 21 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          opacity="0.4"
                          d="M10.4974 18.3327C15.0998 18.3327 18.8307 14.6017 18.8307 9.99935C18.8307 5.39698 15.0998 1.66602 10.4974 1.66602C5.89502 1.66602 2.16406 5.39698 2.16406 9.99935C2.16406 14.6017 5.89502 18.3327 10.4974 18.3327Z"
                          fill="white"
                        />
                        <path
                          d="M13.8609 9.5582L11.3609 7.0582C11.1193 6.81654 10.7193 6.81654 10.4776 7.0582C10.2359 7.29987 10.2359 7.69987 10.4776 7.94154L11.9109 9.37487H7.58594C7.24427 9.37487 6.96094 9.6582 6.96094 9.99987C6.96094 10.3415 7.24427 10.6249 7.58594 10.6249H11.9109L10.4776 12.0582C10.2359 12.2999 10.2359 12.6999 10.4776 12.9415C10.6026 13.0665 10.7609 13.1249 10.9193 13.1249C11.0776 13.1249 11.2359 13.0665 11.3609 12.9415L13.8609 10.4415C14.1026 10.1999 14.1026 9.79987 13.8609 9.5582Z"
                          fill="white"
                        />
                      </svg>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <Footer />

      {/* PIN Success Modal */}
      {showPinSuccess && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-3xl text-center shadow-xl h-[430px] sm:h-[596px] md:w-[522px]  w-full">
            {/* Success Icon */}
            <div className="flex justify-center bg-gradient-to-b rounded-t-3xl mb-6 p-8 pt-[40px] sm:pt-[60px] from-[#FDEFE9] to-[#FEF7F4] h-[180px] sm:h-[270px]">
              <div className="relative">
                <div className="text-[60px] sm:text-[100px]">🎉</div>
              </div>
            </div>
            <div className="p-4 sm:p-8">
              <h2 className="text-[20px] sm:text-[28px] leading-normal font-semibold text-grey-50 mb-2">
                Your Transaction pin has
              </h2>
              <p className="text-[16px] sm:text-[24px] leading-normal mt-[-3px] text-[#808080] mb-2">
                been set successfully
              </p>
              <p className="text-[14px] sm:text-[16px] text-[#808080] mb-6 max-w-[450px] mx-auto">
                You're all set. You can now safely withdraw your funds into your
                account.
              </p>
              {/* Continue Button */}
              <button
                onClick={handlePinSuccessClose}
                className="bg-primary text-white py-2 sm:py-3 mt-4 sm:mt-[30px] px-6 sm:px-8 rounded-full font-medium hover:bg-primary/70 transition-colors"
              >
                Continue
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
