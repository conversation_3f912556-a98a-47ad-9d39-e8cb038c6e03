import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from "react";

export interface Guest {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface GuestListContextType {
  guests: Guest[];
  guestSource: 'manual' | 'upload' | 'email' | 'link';
  addGuest: (guest: Omit<Guest, "id">) => void;
  addGuests: (guests: Omit<Guest, "id">[]) => void;
  removeGuest: (id: number) => void;
  updateGuest: (id: number, updates: Partial<Guest>) => void;
  clearGuests: () => void;
  setGuests: (guests: Guest[], source?: 'manual' | 'upload' | 'email' | 'link') => void;
  getGuestCount: () => number;
  setGuestSource: (source: 'manual' | 'upload' | 'email' | 'link') => void;
}

const GuestListContext = createContext<GuestListContextType | undefined>(
  undefined
);

interface GuestListProviderProps {
  children: ReactNode;
  initialGuests?: Guest[];
  initialSource?: 'manual' | 'upload' | 'email' | 'link';
}

export const GuestListProvider: React.FC<GuestListProviderProps> = ({
  children,
  initialGuests = [],
  initialSource = 'manual',
}) => {
  const [guests, setGuestsState] = useState<Guest[]>(initialGuests);
  const [guestSource, setGuestSourceState] = useState<'manual' | 'upload' | 'email' | 'link'>(initialSource);

  const addGuest = useCallback((guest: Omit<Guest, "id">) => {
    const newGuest: Guest = {
      ...guest,
      id: Date.now() + Math.random(),
    };

    setGuestsState((prev) => [...prev, newGuest]);
  }, []);

  const addGuests = useCallback((newGuests: Omit<Guest, "id">[]) => {
    const guestsWithIds: Guest[] = newGuests.map((guest, index) => ({
      ...guest,
      id: Date.now() + Math.random() + index,
    }));

    setGuestsState((prev) => [...prev, ...guestsWithIds]);
  }, []);

  const removeGuest = useCallback((id: number) => {
    setGuestsState((prev) => prev.filter((guest) => guest.id !== id));
  }, []);

  const updateGuest = useCallback(
    (id: number, updates: Partial<Guest>) => {
      setGuestsState((prev) =>
        prev.map((guest) => (guest.id === id ? { ...guest, ...updates } : guest))
      );
    },
    []
  );

  const clearGuests = useCallback(() => {
    setGuestsState([]);
  }, []);

  const setGuests = useCallback((newGuests: Guest[], source?: 'manual' | 'upload' | 'email' | 'link') => {
    setGuestsState(newGuests);
    if (source) {
      setGuestSourceState(source);
    }
  }, []);

  const getGuestCount = useCallback(() => {
    return guests.length;
  }, [guests]);

  const setGuestSource = useCallback((source: 'manual' | 'upload' | 'email' | 'link') => {
    setGuestSourceState(source);
  }, []);

  const value: GuestListContextType = {
    guests,
    guestSource,
    addGuest,
    addGuests,
    removeGuest,
    updateGuest,
    clearGuests,
    setGuests,
    getGuestCount,
    setGuestSource,
  };

  return (
    <GuestListContext.Provider value={value}>
      {children}
    </GuestListContext.Provider>
  );
};

export const useGuestList = (): GuestListContextType => {
  const context = useContext(GuestListContext);
  if (context === undefined) {
    throw new Error("useGuestList must be used within a GuestListProvider");
  }
  return context;
};
