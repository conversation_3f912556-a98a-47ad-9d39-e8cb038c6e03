Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC6A800000 ntdll.dll
7FFC68870000 KERNEL32.DLL
7FFC67DE0000 KERNELBASE.dll
7FFC694E0000 USER32.dll
7FFC68390000 win32u.dll
7FFC69850000 GDI32.dll
7FFC68160000 gdi32full.dll
7FFC682F0000 msvcp_win.dll
7FFC68440000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC69790000 advapi32.dll
7FFC69430000 msvcrt.dll
7FFC69880000 sechost.dll
7FFC6A220000 RPCRT4.dll
7FFC674A0000 CRYPTBASE.DLL
7FFC683C0000 bcryptPrimitives.dll
7FFC693F0000 IMM32.DLL
