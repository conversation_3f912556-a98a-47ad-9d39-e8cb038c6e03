import React from 'react';

interface AmountDisplayProps {
  amount: number;
  onWithdraw: () => void;
}

export const AmountDisplay: React.FC<AmountDisplayProps> = ({ amount, onWithdraw }) => {
  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm mb-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="text-4xl font-bold text-gray-900 mb-1">
            ₦{amount.toLocaleString()}
          </div>
          <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">
            How much do you want to Withdraw?
          </div>
        </div>
        <button
          onClick={onWithdraw}
          className="bg-primary text-white px-6 py-3 rounded-full font-medium hover:bg-primary-600 transition-colors flex items-center gap-2"
        >
          Withdraw
          <div className="bg-white/20 rounded-full p-1">
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
              <path
                d="M4.5 3L7.5 6L4.5 9"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </button>
      </div>
    </div>
  );
};
