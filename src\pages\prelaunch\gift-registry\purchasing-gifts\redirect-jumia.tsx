import { ArrowCircleRight2 } from "iconsax-react";
import { <PERSON><PERSON> } from "../../../../components/button/onboardingButton";
import { useState, useEffect } from "react";
import { DeliveryAddress } from "./delivery-address";
import { useParams } from "react-router-dom";
import { GuestGiftsAPI, ItemGift } from "../../../../lib/apis/guestGiftsApi";

// Utility function to extract store name from URL
const getStoreNameFromUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();

    // Remove 'www.' if present
    const cleanHostname = hostname.replace(/^www\./, "");

    // Extract the main domain name (before the first dot)
    const domainParts = cleanHostname.split(".");
    const storeName = domainParts[0];

    // Capitalize first letter
    return storeName.charAt(0).toUpperCase() + storeName.slice(1);
  } catch {
    // If URL parsing fails, try to extract from the string
    const match = url.match(/(?:https?:\/\/)?(?:www\.)?([^/.]+)/);
    if (match && match[1]) {
      return match[1].charAt(0).toUpperCase() + match[1].slice(1);
    }
    return "Store";
  }
};

export const JumiaRedirect = () => {
  const [delivery, setDelivery] = useState(false);
  const [itemGift, setItemGift] = useState<ItemGift | null>(null);
  const { eventId, giftId } = useParams();

  useEffect(() => {
    const fetchGiftData = async () => {
      try {
        if (!eventId || !giftId) return;

        const itemGiftsResponse = await GuestGiftsAPI.getItemGifts(eventId);
        const gift = itemGiftsResponse.gifts.find((g) => g.id === giftId);

        if (gift) {
          setItemGift(gift);
        }
      } catch (error) {
        console.error("Error fetching gift data:", error);
      }
    };

    fetchGiftData();
  }, [eventId, giftId]);

  const storeName = itemGift?.item_link
    ? getStoreNameFromUrl(itemGift.item_link)
    : "Store";

  const getStoreUrl = (url: string): string => {
    try {
      return new URL(url).hostname.replace(/^www\./, "");
    } catch {
      // If URL parsing fails, try to extract hostname from string
      const match = url.match(/(?:https?:\/\/)?(?:www\.)?([^/]+)/);
      return match && match[1] ? match[1] : "store.com";
    }
  };

  const storeUrl = itemGift?.item_link
    ? getStoreUrl(itemGift.item_link)
    : "store.com";

  return (
    <>
      <div className="px-6 ">
        <h1 className="text-base font-medium mb-3 mt-4">
          You are about to be redirected to{" "}
          <span className="text-cus-orange-800 italic">{storeName}.com</span>
        </h1>

        <p className="text-gray-600 mb-10 text-base">Here's what to expect:</p>

        <div className="space-y-4 mb-36">
          <div className="flex items-start gap-4 text-left">
            <div className="flex-shrink-0 w-5.5 h-5.5 bg-primary-650 text-white rounded-full flex items-center justify-center font-semibold text-sm">
              1
            </div>
            <div>
              <p className="text-grey-550 italic text-sm">
                You'll be{" "}
                <span className="font-semibold text-black">
                  redirected to {storeUrl}
                </span>{" "}
                <span className="text-gray-500">to purchase your gift</span>
              </p>
            </div>
          </div>

          <div className="flex items-start gap-4 text-left">
            <div className="flex-shrink-0 w-5.5 h-5.5 bg-primary-650 text-white rounded-full flex items-center justify-center font-semibold text-sm">
              2
            </div>
            <div>
              <p className="text-gray-700">
                <span className="text-gray-550 italic  text-sm">
                  After purchase, come back to this tab to
                </span>{" "}
                <span className="font-semibold italic text-sm text-black">
                  confirm your purchase
                </span>
              </p>
            </div>
          </div>
        </div>

        <Button
          variant="primary"
          size="md"
          onClick={() => setDelivery(true)}
          className={`text-white  mb-20 mt-5  bg-primary-650 `}
          iconRight={
            <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
          }
        >
          Continue to {storeName}
        </Button>
      </div>
      {delivery && <DeliveryAddress onClose={() => setDelivery(false)} />}
    </>
  );
};
