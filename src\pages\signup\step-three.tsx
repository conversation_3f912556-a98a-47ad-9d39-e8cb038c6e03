/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation } from "@tanstack/react-query";
import { Button } from "../../components/button/button";
import { PasswordInput } from "../../components/inputs/password-input/password-input";
import { AuthServices } from "../../lib/services/auth";
import { toast } from "react-toastify";
import { useForm } from "react-hook-form";
import { useUserAuthStore } from "../../lib/store/auth";
import { useEventStore } from "../../lib/store/event";
import { useNavigate } from "react-router-dom";
import { events } from "../../lib/services/events";

type FormData = {
  password: string;
  confirmPassword: string;
};
export function StepThree({
  email,
  first_name,
  last_name,
}: {
  email: string;
  first_name: string;
  last_name: string;
}) {
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
  } = useForm<FormData>({ mode: "onChange" });
  const setAuthData = useUserAuthStore((state) => state.setAuthData);
  const setToolStatus = useUserAuthStore((state) => state.setToolStatus);
  const setUserEvents = useEventStore((state) => state.setUserEvents);
  const navigate = useNavigate();
  const mutation = useMutation({
    mutationFn: (data: FormData) =>
      AuthServices.completeRegistration({
        email: email,
        first_name: first_name,
        last_name: last_name,
        password: data.password,
      }),
    onSuccess: async (data) => {
      const access_token = data?.data?.access_token;
      const refresh_token = data?.data?.refresh_token;
      const access_token_expires_at = data?.data?.access_token_expires_at;
      const refresh_token_expires_at = data?.data?.refresh_token_expires_at;
      const userData = data?.data?.user_data;

      setAuthData(
        access_token,
        {
          email: userData?.email,
          first_name: userData?.first_name,
          last_name: userData?.last_name,
          id: userData?.id,
          password_set: userData?.password_set,
          profile_picture: userData?.profile_picture,
          transaction_pin_set: userData?.transaction_pin_set,
        },
        refresh_token,
        access_token_expires_at,
        refresh_token_expires_at
      );

      try {
        // Fetch user events and tool status in parallel
        const [eventsResponse, toolStatusResponse] = await Promise.all([
          events.getEventForAuthUsers(),
          AuthServices.getToolStatus(),
        ]);

        const userEvents = eventsResponse?.data?.events || [];
        const eventsMeta = eventsResponse?.data?.meta || null;
        const toolStatus = toolStatusResponse?.data || null;

        setUserEvents(userEvents, eventsMeta);
        if (toolStatus) {
          setToolStatus(toolStatus);
        }

        toast.success("Account created successfully! Welcome to EventPark!");

        // After signup completion, always take user to prelaunch page first
        navigate("/prelaunch");
      } catch (error) {
        console.error("Failed to fetch user data:", error);
        toast.success("Account created successfully!");
        // On error, also redirect to prelaunch for new users
        navigate("/prelaunch");
      }
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });
  const onSubmit = handleSubmit((data) => {
    mutation.mutate(data);
  });
  return (
    <div>
      <div className="mb-[4vh]">
        <h1 className="font-semibold text-[32px]">Setup a Password</h1>
        <h3 className="text-grey-100 mb-9">
          Bringing your event from planning to completion!
        </h3>

        <form onSubmit={onSubmit}>
          <PasswordInput
            label="New Password"
            id="signup-password"
            {...register("password", {
              required: "Password is required",
              minLength: {
                value: 8,
                message: "Password must be at least 8 characters",
              },
              // pattern: {
              //   value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/,
              //   message:
              //     'Password must contain uppercase, lowercase, and number',
              // },
            })}
            placeholder="Enter your password"
            error={errors.password?.message}
          />
          <div className="mt-5">
            <PasswordInput
              label="Confirm Password"
              id="signup-confirm-password"
              {...register("confirmPassword", {
                required: "Please confirm your password",
                validate: (value) =>
                  value === watch("password") || "Passwords do not match",
              })}
              placeholder="Re-enter your Password"
              error={errors.confirmPassword?.message}
            />
          </div>
          <Button
            type="submit"
            variant="primary"
            className="mt-10"
            disabled={!isValid || mutation.isPending}
            isLoading={mutation.isPending}
          >
            Create Account
          </Button>
        </form>
      </div>
    </div>
  );
}
