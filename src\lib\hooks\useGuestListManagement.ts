/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  events,
  GuestData,
  CreateGuestListPayload,
  CreateGuestFilePayload,
  GuestParams,
} from '../services/events';
import { toast } from 'react-toastify';
import { useCallback, useState, useEffect } from 'react';

export const useGuestListManagement = () => {
  const queryClient = useQueryClient();

  const createGuestListMutation = useMutation({
    mutationFn: (payload: CreateGuestListPayload) =>
      events.createGuestList(payload),
    onSuccess: (response, variables) => {
      console.log('✅ Guest list created successfully:', response);
      queryClient.invalidateQueries({ queryKey: ['userEvents'] });
      queryClient.invalidateQueries({ queryKey: ['guestList', variables.id] });
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || 'Failed to create guest list';
      toast.error(errorMessage);
    },
  });

  const createGuestListFromFileMutation = useMutation({
    mutationFn: (payload: CreateGuestFilePayload) =>
      events.createGuestListFromFile(payload),
    onSuccess: (response, variables) => {
      console.log('✅ Guest list created from file successfully:', response);
      toast.success('Guest list uploaded successfully!');

      queryClient.invalidateQueries({ queryKey: ['userEvents'] });
      queryClient.invalidateQueries({ queryKey: ['guestList', variables.id] });
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || 'Failed to upload guest list';
      toast.error(errorMessage);
    },
  });

  const activateGuestListMutation = useMutation({
    mutationFn: (eventId: string) => events.activateGuestList(eventId),
    onSuccess: (response, eventId) => {
      console.log('✅ Guest list activated successfully:', response);
      toast.success('Guest list activated successfully!');
      queryClient.invalidateQueries({ queryKey: ['userEvents'] });
      queryClient.invalidateQueries({ queryKey: ['guestList', eventId] });
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || 'Failed to activate guest list';
      toast.error(errorMessage);
    },
  });

  const createGuestList = useCallback(
    async (
      eventId: string,
      guests: GuestData[],
      inviteType: string = 'manual'
    ) => {
      if (!eventId) {
        throw new Error('Event ID is required');
      }

      // if (!guests || guests.length === 0) {
      //   throw new Error("At least one guest is required");
      // }
      // const invalidGuests = guests.filter(
      //   (guest) =>
      //     !guest.email ||
      //     !guest.first_name ||
      //     !guest.last_name ||
      //     !guest.phone_number
      // );

      // if (invalidGuests.length > 0) {
      //   throw new Error(
      //     "All guests must have email, first name, last name, and phone number"
      //   );
      // }

      const payload: CreateGuestListPayload = {
        id: eventId,
        guests,
        invite_type: inviteType,
      };
      return createGuestListMutation.mutateAsync(payload);
    },
    [createGuestListMutation]
  );

  const uploadGuestListFile = useCallback(
    async (eventId: string, file: File, inviteType: string = 'file_upload') => {
      if (!eventId) {
        throw new Error('Event ID is required');
      }

      if (!file) {
        throw new Error('File is required');
      }
      const allowedTypes = ['.csv', '.xlsx', '.xls'];
      const fileExtension = file.name
        .toLowerCase()
        .substring(file.name.lastIndexOf('.'));

      if (!allowedTypes.includes(fileExtension)) {
        throw new Error('Please upload a CSV or Excel file');
      }

      const payload: CreateGuestFilePayload = {
        id: eventId,
        file,
        invite_type: inviteType,
      };

      console.log('📁 Uploading guest list file:', {
        eventId,
        fileName: file.name,
        fileSize: file.size,
      });
      return createGuestListFromFileMutation.mutateAsync(payload);
    },
    [createGuestListFromFileMutation]
  );

  const activateGuestList = useCallback(
    async (eventId: string) => {
      if (!eventId) {
        throw new Error('Event ID is required');
      }

      console.log('🚀 Activating guest list for event:', eventId);
      return activateGuestListMutation.mutateAsync(eventId);
    },
    [activateGuestListMutation]
  );

  const transformGuestData = useCallback(
    (
      uiGuests: Array<{
        firstName: string;
        lastName: string;
        email: string;
        phone: string;
      }>,
      guestSource?: 'manual' | 'upload' | 'email' | 'link'
    ): GuestData[] => {
      return uiGuests.map((guest) => {
        const baseData = {
          email: guest.email.trim().toLowerCase(),
        };

        // For email-only invites, only send email field
        if (guestSource === 'email') {
          return baseData as GuestData;
        }

        // For other sources, include all fields
        return {
          ...baseData,
          first_name: guest.firstName?.trim() || '',
          last_name: guest.lastName?.trim() || '',
          phone_number: guest.phone
            ? guest.phone.startsWith('+')
              ? guest.phone
              : `+234${guest.phone}`
            : '',
        };
      });
    },
    []
  );

  return {
    createGuestListMutation,
    createGuestListFromFileMutation,
    activateGuestListMutation,

    createGuestList,
    uploadGuestListFile,
    activateGuestList,
    transformGuestData,

    isCreatingGuestList: createGuestListMutation.isPending,
    isUploadingFile: createGuestListFromFileMutation.isPending,
    isActivatingGuestList: activateGuestListMutation.isPending,
    isLoading:
      createGuestListMutation.isPending ||
      createGuestListFromFileMutation.isPending ||
      activateGuestListMutation.isPending,

    createError: createGuestListMutation.error,
    uploadError: createGuestListFromFileMutation.error,
    activateError: activateGuestListMutation.error,
  };
};

export const useGuests = (id: string, params?: GuestParams) => {
  return useQuery({
    queryKey: ['guests', id, params],
    queryFn: () => events.getGuestsForAnAuthUser(id, params),
    enabled: !!id,
  });
};

export const useGuestSearch = (id: string) => {
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isSearchLoading, setIsSearchLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(
    null
  );

  const performSearch = useCallback(
    async (query: string) => {
      // Clear existing timer
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      if (
        !query.trim() ||
        query === '__INIT_SEARCH__' ||
        query === '__CANCEL_SEARCH__'
      ) {
        if (query === '__INIT_SEARCH__') {
          setIsSearching(true);
          setSearchQuery('');
          setSearchResults([]);
          setIsSearchLoading(false);
        } else {
          setIsSearching(false);
          setSearchResults([]);
          setSearchQuery('');
          setIsSearchLoading(false);
        }
        return;
      }

      setIsSearching(true);
      setSearchQuery(query);

      const timer = setTimeout(async () => {
        setIsSearchLoading(true);
        try {
          const response = await events.getGuestsForAnAuthUser(id, {
            search: query,
            per_page: 50,
          });

          if (response?.data?.guests) {
            setSearchResults(response.data.guests);
          }
        } catch (error) {
          console.error('Error searching guests:', error);
          setSearchResults([]);
        } finally {
          setIsSearchLoading(false);
        }
      }, 500);

      setDebounceTimer(timer);
    },
    [id, debounceTimer]
  );

  const clearSearch = useCallback(() => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    setIsSearching(false);
    setSearchResults([]);
    setSearchQuery('');
    setIsSearchLoading(false);
    setDebounceTimer(null);
  }, [debounceTimer]);

  return {
    searchResults,
    isSearching,
    isSearchLoading,
    searchQuery,
    performSearch,
    clearSearch,
  };
};

export const useInfiniteGuests = (id: string, perPage: number = 5) => {
  const [allGuests, setAllGuests] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMorePages, setHasMorePages] = useState(true);
  const [totalGuests, setTotalGuests] = useState(0);

  const {
    data: initialData,
    isLoading: isInitialLoading,
    isError,
  } = useQuery({
    queryKey: ['guests', id, { page: 1, per_page: perPage }],
    queryFn: () =>
      events.getGuestsForAnAuthUser(id, { page: 1, per_page: perPage }),
    enabled: !!id,
  });

  useEffect(() => {
    if (initialData?.data) {
      console.log('Initial data received:', {
        guests: initialData.data.guests?.length,
        meta: initialData.data.meta,
        hasNextPage: initialData.data.meta?.next_page,
      });
      setAllGuests(initialData.data.guests || []);
      setTotalGuests(initialData.data.meta?.total || 0);
      setHasMorePages(initialData.data.meta?.next_page || false);
    }
  }, [initialData]);

  const loadMore = useCallback(
    async (status?: string) => {
      console.log('loadMore called with:', {
        status,
        isLoadingMore,
        hasMorePages,
        id,
        currentPage,
      });

      if (isLoadingMore || !hasMorePages || !id) {
        console.log('loadMore blocked:', { isLoadingMore, hasMorePages, id });
        return;
      }

      setIsLoadingMore(true);
      try {
        const nextPage = currentPage + 1;
        const response = await events.getGuestsForAnAuthUser(id, {
          page: nextPage,
          per_page: perPage,
        });

        if (response?.data?.guests) {
          setAllGuests((prev) => {
            const newGuests = [...prev, ...response.data.guests];
            console.log('Updated guests count:', newGuests.length);
            return newGuests;
          });
          setCurrentPage(nextPage);
          setHasMorePages(response.data.meta?.next_page || false);
        }
      } catch (error) {
        console.error('Error loading more guests:', error);
      } finally {
        setIsLoadingMore(false);
      }
    },
    [id, currentPage, perPage, isLoadingMore, hasMorePages]
  );

  const reset = useCallback(() => {
    setAllGuests([]);
    setCurrentPage(1);
    setIsLoadingMore(false);
    setHasMorePages(true);
  }, []);

  return {
    guests: allGuests,
    totalGuests,
    isLoading: isInitialLoading,
    isLoadingMore,
    hasMorePages,
    isError,
    loadMore,
    reset,
    meta: initialData?.data?.meta,
  };
};
