import { ArrowCircleLeft2, Tag2 } from "iconsax-react";
import { <PERSON><PERSON> } from "../../../../components/button/onboardingButton";
import { StepProgress } from "../../../../components/step-progress/step-progress";
import { useEffect, useState } from "react";
import { GifterDetails } from "./gifter-details";
import { GiftReservations } from "./gift-reservations";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  EventDetailsResponse,
  GuestGiftsAPI,
  ItemGift,
} from "../../../../lib/apis/guestGiftsApi";
// import { guestTokenManager } from "../../../../lib/utils/guestTokenManager";
import { toast } from "react-toastify";
// import { JumiaRedirect } from './redirect-jumia';

export const ItemsWithCash = () => {
  const navigate = useNavigate();
  const { eventId, giftId } = useParams();
  const [searchParams] = useSearchParams();

  // Check if we have an existing reservation - if so, start at step 2
  const existingReservationId = searchParams.get("existing_reservation");
  const [activeStep, setActiveStep] = useState(existingReservationId ? 2 : 1);
  const [completedSteps, setCompletedSteps] = useState<number[]>(
    existingReservationId ? [1] : []
  );
  const [itemGift, setItemGift] = useState<ItemGift | null>(null);
  const [eventDetails, setEventDetails] = useState<EventDetailsResponse | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    window.scrollTo(0, 0);
    fetchGiftData();
  }, [eventId, giftId]);

  const fetchGiftData = async () => {
    try {
      setLoading(true);

      if (!eventId || !giftId) {
        toast.error("Missing event or gift information");
        navigate(-1);
        return;
      }

      // Fetch item gifts to find the specific gift
      const itemGiftsResponse = await GuestGiftsAPI.getItemGifts(eventId);
      const gift = itemGiftsResponse.gifts.find((g) => g.id === giftId);

      if (!gift) {
        toast.error("Gift not found");
        navigate(-1);
        return;
      }

      setItemGift(gift);

      // Fetch event details for host name and delivery address
      const eventResponse = await GuestGiftsAPI.getEventDetails(eventId);
      setEventDetails(eventResponse);
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to load gift details");
      navigate(-1);
    } finally {
      setLoading(false);
    }
  };
  const steps = [
    { id: 1, name: "Gifter's details" },
    { id: 2, name: "Gift Reservation" },
  ];

  const handleStepChange = (stepId: number) => {
    if (
      completedSteps.includes(stepId) ||
      stepId === activeStep ||
      stepId === activeStep - 1
    ) {
      setActiveStep(stepId);
    }
  };

  if (loading) {
    return (
      <div className="bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] min-h-screen flex items-center justify-center">
        <div className="text-primary text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p>Loading gift details...</p>
        </div>
      </div>
    );
  }

  if (!itemGift || !eventDetails) {
    return (
      <div className="bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] min-h-screen flex items-center justify-center">
        <div className="text-white text-center">
          <p>Gift not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className=" bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]  pb-36">
      <header className="w-full h-20 border-b border-[#f5f6fe]">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex items-center h-20">
            <div
              onClick={() => {
                window.location.href =
                  "https://customer-preprod.eventpark.africa/login";
              }}
              className="flex items-center cursor-pointer gap-1"
            >
              <img className="w-6 h-6" alt="Vector" src="/vector.svg" />
              <div className="font-bold text-xl text-center tracking-[-0.40px] leading-5 whitespace-nowrap">
                <span className="text-[#000073] tracking-[-0.08px]">
                  EventPark
                </span>
                <span className="text-[#ff6630] tracking-[-0.08px]">.</span>
              </div>
            </div>
          </div>
        </div>
      </header>
      <div className="pt-14 mx-auto max-w-[696px] w-full px-2 md:px-0">
        <Button
          variant="primary"
          size="md"
          onClick={() => navigate(-1)}
          className="text-primary-650 bg-white"
          iconLeft={
            <ArrowCircleLeft2 size="20" color="#4D55F2" variant="Bulk" />
          }
        >
          Back
        </Button>
        <div className="mt-5 md:flex justify-between bg-white rounded-2xl ">
          <div className="md:p-4 p-2 text-center md:text-start border-r border-grey-150 flex flex-col items-center md:items-start justify-between ">
            <div>
              <img
                src={itemGift.image_preview_url || "/placeholder-gift.png"}
                alt={itemGift.name}
                className="w-[250px] h-48 object-cover rounded-lg"
              />
              <p className="text-[22px] font-medium text-grey-750 mt-5.5">
                {itemGift.name}
              </p>
              <p className="text-base text-grey-100">{itemGift.description}</p>
              <div className="mt-3 mx-auto md:mx-0 flex items-center gap-1.5 bg-light-blue-150 w-fit py-1.5 px-2.5 rounded-2xl">
                <Tag2 size={12} variant="Bulk" color="#5856D6 " />
                <span className="text-perple-50 text-sm font-bold">
                  ₦{parseFloat(itemGift.price).toLocaleString()}
                </span>
              </div>
            </div>
            <div className="">
              <p className="text-sm text-grey-100">Total Amount</p>
              <p className="text-base font-medium text-grey-750">
                ₦{parseFloat(itemGift.price).toLocaleString()}
              </p>
            </div>
          </div>
          <div className=" flex-1 pt-6 ">
            <div className="md:px-5 px-1">
              <h2 className="text-[22px] font-medium text-grey-750">
                Reserve a gift for {eventDetails.host_first_name}{" "}
                {eventDetails.host_last_name}
              </h2>
              <p className="text-base text-grey-100">{itemGift.description}</p>
            </div>
            <StepProgress
              steps={steps}
              activeStep={activeStep}
              completedSteps={completedSteps}
              className="md:pl-4 !gap-1"
              onStepChange={handleStepChange}
            />{" "}
            {activeStep === 1 && (
              <GifterDetails
                onContinue={() => {
                  setCompletedSteps((prev) => [...new Set([...prev, 1])]);
                  setActiveStep(2);
                }}
              />
            )}
            {activeStep === 2 && <GiftReservations />}
          </div>
        </div>
      </div>{" "}
    </div>
  );
};
