import { ArrowCircleRight2 } from 'iconsax-react';
import { useState, useRef, useEffect } from 'react';
import { AuthServices } from '../../lib/services/auth';
import { toast } from 'react-toastify';
import { useUserAuthStore } from '../../lib/store/auth';

interface TransactionPinModalProps {
  onSave: () => void;
  onClose: () => void;
}

export const TransactionPinModal: React.FC<TransactionPinModalProps> = ({
  onSave,
  onClose,
}) => {
  const { userData, setAuthData, userAppToken, refreshToken } =
    useUserAuthStore();
  const hasExistingPin = userData?.transaction_pin_set || false;

  const [oldPin, setOldPin] = useState(['', '', '', '']);
  const [newPin, setNewPin] = useState(['', '', '', '']);
  const [currentStep, setCurrentStep] = useState<'old' | 'new'>(
    hasExistingPin ? 'old' : 'new'
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const oldInputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const newInputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (currentStep === 'old' && oldInputRefs.current[0]) {
      oldInputRefs.current[0].focus();
    } else if (currentStep === 'new' && newInputRefs.current[0]) {
      newInputRefs.current[0].focus();
    }
  }, [currentStep]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  const handleOldPinChange = (index: number, value: string) => {
    if (value.length > 1) return;
    if (!/^\d*$/.test(value)) return;

    const newOldPin = [...oldPin];
    newOldPin[index] = value;
    setOldPin(newOldPin);

    if (value && index < 3) {
      oldInputRefs.current[index + 1]?.focus();
    }
  };

  const handleOldPinKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !oldPin[index] && index > 0) {
      oldInputRefs.current[index - 1]?.focus();
    }
  };

  const handleOldPinPaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').slice(0, 4);
    if (!/^\d+$/.test(pastedData)) return;

    const newOldPin = [...oldPin];
    for (let i = 0; i < pastedData.length && i < 4; i++) {
      newOldPin[i] = pastedData[i];
    }
    setOldPin(newOldPin);
    const nextIndex = Math.min(pastedData.length, 3);
    oldInputRefs.current[nextIndex]?.focus();
  };

  const handleNewPinChange = (index: number, value: string) => {
    if (value.length > 1) return;
    if (!/^\d*$/.test(value)) return;

    if (error) {
      setError(null);
    }

    const updatedNewPin = [...newPin];
    updatedNewPin[index] = value;
    setNewPin(updatedNewPin);

    if (value && index < 3) {
      newInputRefs.current[index + 1]?.focus();
    }
  };

  const handleNewPinKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !newPin[index] && index > 0) {
      newInputRefs.current[index - 1]?.focus();
    }
  };

  const handleNewPinPaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').slice(0, 4);
    if (!/^\d+$/.test(pastedData)) return;
    const updatedNewPin = [...newPin];
    for (let i = 0; i < pastedData.length && i < 4; i++) {
      updatedNewPin[i] = pastedData[i];
    }
    setNewPin(updatedNewPin);
    const nextIndex = Math.min(pastedData.length, 3);
    newInputRefs.current[nextIndex]?.focus();
  };

  const isOldPinComplete = oldPin.every((digit) => digit !== '');
  const isNewPinComplete = newPin.every((digit) => digit !== '');

  const refreshUserData = async () => {
    try {
      const userResponse = await AuthServices.getUser();
      const latestUserData = userResponse.data;

      if (userAppToken && userData) {
        setAuthData(
          userAppToken,
          {
            email: latestUserData.email,
            first_name: latestUserData.first_name,
            last_name: latestUserData.last_name,
            id: latestUserData.id,
            transaction_pin_set: latestUserData.transaction_pin_set,
            password_set: latestUserData.password_set,
            profile_picture:
              latestUserData.profile_picture_url ||
              latestUserData.profile_picture,
          },
          refreshToken || undefined
        );
      }
    } catch (error) {
      console.error('Failed to refresh user data:', error);
    }
  };

  const handleContinue = async () => {
    if (currentStep === 'old' && isOldPinComplete) {
      setError(null);
      setCurrentStep('new');
    } else if (currentStep === 'new' && isNewPinComplete) {
      setIsLoading(true);
      setError(null);

      try {
        const newPinString = newPin.join('');

        if (hasExistingPin) {
          const oldPinString = oldPin.join('');
          await AuthServices.updateUserTransactionPin({
            old_pin: oldPinString,
            new_pin: newPinString,
          });
        } else {
          await AuthServices.createUserTransactionPin({
            pin: newPinString,
          });
        }

        await refreshUserData();

        onSave();
      } catch (err: unknown) {
        const errorMessage =
          (err as { response?: { data?: { message?: string } } })?.response
            ?.data?.message ||
          `Failed to ${
            hasExistingPin ? 'update' : 'create'
          } transaction PIN. Please try again.`;
        toast.error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const canContinue =
    currentStep === 'old' ? isOldPinComplete : isNewPinComplete && !isLoading;

  return (
    <div
      ref={modalRef}
      className="bg-white rounded-2xl p-4 mb-5 shadow-[0px_12px_120px_0px_#5F5F5F0F]">
      <div className="flex-1">
        <div className="tracking-[0.12em] text-primary-50 font-medium text-sm">
          TRANSACTION PIN
        </div>
        <h3 className="text-2xl font-semibold mt-3.5 mb-2">
          {currentStep === 'old'
            ? 'Enter Current PIN'
            : hasExistingPin
            ? 'Set New Transaction Pin'
            : 'Create Transaction Pin'}
        </h3>
        <p className="text-base text-grey-960 mb-8">
          {currentStep === 'old'
            ? 'Please enter your current transaction PIN to continue'
            : 'For safe and secure withdrawals'}
        </p>

        {currentStep === 'old' && hasExistingPin && (
          <div className="mb-8 border py-5 px-4 rounded-xl border-[#F0F0F0]">
            <p className="text-lg  mb-6 max-w-[382px]">
              Enter your current pin
            </p>

            <div className="flex gap-2 ">
              {oldPin.map((digit, index) => (
                <input
                  key={index}
                  ref={(el) => {
                    if (el) oldInputRefs.current[index] = el;
                  }}
                  type="password"
                  inputMode="numeric"
                  placeholder="•"
                  maxLength={1}
                  value={digit}
                  onChange={(e) => handleOldPinChange(index, e.target.value)}
                  onKeyDown={(e) => handleOldPinKeyDown(index, e)}
                  onPaste={handleOldPinPaste}
                  className={`w-[56px] h-[56px] placeholder:text-grey-200 text-center border border-primary-950 rounded-full text-[48px] font-medium leading-[60px] tracking-[-2%] shadow-xs shadow-[hsla(220,29%,5%,0.05)] ${
                    digit
                      ? 'text-[#00000D] border-[#DBDDFC]'
                      : 'border-stroke-gray-300'
                  } focus:outline-none focus:border-[#A6AAF9] focus:text-primary focus:shadow-[0px_0px_0px_4px_#DBDDFC]`}
                />
              ))}
            </div>
          </div>
        )}

        {currentStep === 'new' && (
          <div className="mb-8 border py-5 px-4 rounded-xl border-[#F0F0F0]">
            <p className="text-lg  mb-6 max-w-[382px]">
              {hasExistingPin
                ? 'Enter your new preferred 4 digit transaction pin'
                : 'Create your preferred 4 digit transaction pin to securely perform your transactions'}
            </p>

            <div className="flex gap-2 ">
              {newPin.map((digit, index) => (
                <input
                  key={index}
                  ref={(el) => {
                    if (el) newInputRefs.current[index] = el;
                  }}
                  type="password"
                  inputMode="numeric"
                  placeholder="•"
                  maxLength={1}
                  value={digit}
                  onChange={(e) => handleNewPinChange(index, e.target.value)}
                  onKeyDown={(e) => handleNewPinKeyDown(index, e)}
                  onPaste={handleNewPinPaste}
                  className={`w-[56px] h-[56px] placeholder:text-grey-200 text-center border border-primary-950 rounded-full text-[48px] font-medium leading-[60px] tracking-[-2%] shadow-xs shadow-[hsla(220,29%,5%,0.05)] ${
                    digit
                      ? 'text-[#00000D] border-[#DBDDFC]'
                      : 'border-stroke-gray-300'
                  } focus:outline-none focus:border-[#A6AAF9] focus:text-primary focus:shadow-[0px_0px_0px_4px_#DBDDFC]`}
                />
              ))}
            </div>
          </div>
        )}

        {/* {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )} */}

        <button
          onClick={handleContinue}
          disabled={!canContinue}
          className={`flex items-center gap-1 px-4 py-3 rounded-full text-sm font-semibold cursor-pointer transition-colors ${
            canContinue
              ? 'bg-primary text-white hover:bg-primary-600'
              : 'bg-grey-200 text-grey-400 cursor-not-allowed'
          }`}>
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              {hasExistingPin ? 'Updating PIN...' : 'Creating PIN...'}
            </>
          ) : (
            <>
              {currentStep === 'old'
                ? 'Continue'
                : hasExistingPin
                ? 'Update PIN'
                : 'Create PIN'}
              <ArrowCircleRight2 size={20} color="#fff" variant="Bulk" />
            </>
          )}
        </button>
      </div>
    </div>
  );
};
