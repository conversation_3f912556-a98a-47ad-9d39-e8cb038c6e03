<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Engagement Party Invitation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Alice:wght@400&family=Water+Brush:wght@400&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Alice", serif;
        background-color: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .invitation-container {
        position: relative;
        width: 1240px;
        height: 1748px;
        background-image: url("https://customer-preprod.eventpark.africa/assets/email-templates/invitation-background.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        max-width: 100%;
        max-height: 100vh;
        object-fit: contain;
      }

      .text-element {
        position: absolute;
        color: #ffffff;
      }

      .invitation-title {
        font-family: "Alice", serif;
        font-weight: 400;
        font-size: 43px;
        line-height: 1.2093023255813953em;
        letter-spacing: 2%;
        text-align: center;
        left: 312px;
        top: 720px;
        width: 617px;
        height: 104px;
      }

      .couple-names {
        font-family: "Water Brush", cursive;
        font-weight: 400;
        font-size: 170px;
        line-height: 1.0588235294117647em;
        letter-spacing: 2%;
        color: #bf9346;
        left: 181px;
        top: 852px;
        width: 878px;
        height: 180px;
        display: flex;
        justify-content: center;
        align-items: center;
        transform-origin: center center;
      }

      /* Dynamic scaling based on character count */
      /* Base: 20 chars = scale(1.0), 26 chars = scale(0.6) */
      /* Formula: scale = 1.0 - ((char_count - 20) * 0.067) */

      .couple-names[data-length="21"] {
        transform: scale(0.93);
      }
      .couple-names[data-length="22"] {
        transform: scale(0.87);
      }
      .couple-names[data-length="23"] {
        transform: scale(0.8);
      }
      .couple-names[data-length="24"] {
        transform: scale(0.73);
      }
      .couple-names[data-length="25"] {
        transform: scale(0.67);
      }
      .couple-names[data-length="26"] {
        transform: scale(0.6);
      }
      .couple-names[data-length="27"] {
        transform: scale(0.53);
      }
      .couple-names[data-length="28"] {
        transform: scale(0.47);
      }
      .couple-names[data-length="29"] {
        transform: scale(0.4);
      }
      .couple-names[data-length="30"] {
        transform: scale(0.35);
      }
      .couple-names[data-length="31"] {
        transform: scale(0.3);
      }
      .couple-names[data-length="32"] {
        transform: scale(0.25);
      }

      /* For very long names, use minimum scale */
      .couple-names[data-length="33"],
      .couple-names[data-length="34"],
      .couple-names[data-length="35"],
      .couple-names[data-length="36"],
      .couple-names[data-length="37"],
      .couple-names[data-length="38"],
      .couple-names[data-length="39"],
      .couple-names[data-length="40"] {
        transform: scale(0.2);
      }

      .last-name-phillips {
        font-family: "Alice", serif;
        font-weight: 400;
        font-size: 48px;
        line-height: 1.1429999669392903em;
        letter-spacing: 2%;
        color: #bf9346;
        left: 249px;
        top: 1060px;
        width: 218px;
        height: 55px;
      }

      .last-name-brown {
        font-family: "Alice", serif;
        font-weight: 400;
        font-size: 48px;
        line-height: 1.1429999669392903em;
        letter-spacing: 2%;
        color: #bf9346;
        left: 792px;
        top: 1060px;
        width: 179px;
        height: 55px;
      }

      .event-date {
        font-family: "Alice", serif;
        font-weight: 400;
        font-size: 57px;
        line-height: 1.1430000171326755em;
        letter-spacing: 2.0000000000000004%;
        left: 356px;
        top: 1218px;
        width: 528px;
        height: 65px;
        text-align: center;
      }

      .event-time {
        font-family: "Alice", serif;
        font-weight: 400;
        font-size: 41px;
        line-height: 1.2682926829268293em;
        letter-spacing: 2%;
        text-transform: uppercase;
        text-align: center;
        left: 402px;
        top: 1347px;
        width: 450px;
        height: 52px;
      }

      .event-address {
        font-family: "Alice", serif;
        font-weight: 400;
        font-size: 41px;
        line-height: 1.2682926829268293em;
        letter-spacing: 2%;
        text-transform: uppercase;
        text-align: center;
        left: 153px;
        top: 1407px;
        width: 934px;
        height: 52px;
      }

      .rsvp-info {
        font-family: "Alice", serif;
        font-weight: 400;
        font-size: 41px;
        line-height: 1.2682926829268293em;
        letter-spacing: 2%;
        text-transform: uppercase;
        text-align: center;
        left: 332px;
        top: 1479px;
        width: 576px;
        height: 52px;
      }

      .please-reply {
        font-family: "Water Brush", cursive;
        font-weight: 400;
        font-size: 58px;
        line-height: 0.896551724137931em;
        letter-spacing: 1.9999999999999998%;
        text-align: center;
        left: 511px;
        top: 1612px;
        width: 219px;
        height: 52px;
      }

      @media screen and (max-width: 1280px) {
        .invitation-container {
          transform: scale(0.8);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 1024px) {
        .invitation-container {
          transform: scale(0.6);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 768px) {
        .invitation-container {
          transform: scale(0.4);
          transform-origin: center center;
        }
      }
    </style>
  </head>
  <body>
    <div class="invitation-container">
      <div class="text-element invitation-title">{{{event_header}}}</div>

      <div class="text-element couple-names">
        {{#if two_celebrant}}
        <span>{{{first_celebrant}}}&nbsp;</span> +
        <span>&nbsp;{{{second_celebrant}}}</span>
        {{else}} {{{celebrant_name}}} {{/if}}
      </div>

      <div class="text-element event-date">{{{event_date}}}</div>

      <div class="text-element event-time">{{{event_time}}}</div>

      <div class="text-element event-address">{{{event_address}}}</div>

      <div class="text-element rsvp-info">RSVP: {{{rsvp_contact}}}</div>

      <div class="text-element please-reply">please reply</div>
    </div>

    <script>
      function calculateCoupleNamesLength() {
        const coupleNamesElement = document.querySelector(".couple-names");
        if (coupleNamesElement) {
          // Get the text content and calculate total character count
          const textContent =
            coupleNamesElement.textContent || coupleNamesElement.innerText;
          const cleanText = textContent.replace(/\s+/g, " ").trim(); // Normalize whitespace
          const totalLength = cleanText.length;

          // Apply the data-length attribute for CSS scaling
          coupleNamesElement.setAttribute(
            "data-length",
            totalLength.toString()
          );

          console.log(`Celebrant names length: ${totalLength} characters`);
          console.log(`Applied scaling for length: ${totalLength}`);
        }
      }

      // Run the calculation when the page loads
      document.addEventListener("DOMContentLoaded", calculateCoupleNamesLength);

      // Also run it after a short delay to ensure all content is loaded
      setTimeout(calculateCoupleNamesLength, 100);
    </script>
  </body>
</html>
