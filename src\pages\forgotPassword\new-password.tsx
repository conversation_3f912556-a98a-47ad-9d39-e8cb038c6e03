/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from "react";
import { Button } from "../../components/button/button";
import { useMutation } from "@tanstack/react-query";
import { AuthServices } from "../../lib/services/auth";
import { toast } from "react-toastify";
import { useUserAuthStore } from "../../lib/store/auth";

type FormData = {
  password: string;
};
export const NewPasswordStep = ({
  email,
  fourthStep,
}: {
  email: string;
  fourthStep: () => void;
}) => {
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [passwordValidation, setPasswordValidation] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false,
  });
  const setAuthData = useUserAuthStore((state) => state.setAuthData);

  // Password validation function
  const validatePassword = (password: string) => {
    const validation = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[!@#$%^&*()_+\-=[\]{}|;:,.<>?]/.test(password),
    };
    setPasswordValidation(validation);
    return Object.values(validation).every(Boolean);
  };

  useEffect(() => {
    if (newPassword) {
      const isValidPassword = validatePassword(newPassword);

      if (!isValidPassword) {
        setPasswordError("Password does not meet all requirements");
      } else if (confirmPassword && newPassword !== confirmPassword) {
        setPasswordError("Passwords do not match");
      } else {
        setPasswordError("");
      }
    } else {
      // Reset validation when password is empty
      setPasswordValidation({
        length: false,
        uppercase: false,
        lowercase: false,
        number: false,
        special: false,
      });
      setPasswordError("");
    }
  }, [newPassword, confirmPassword]);
  const mutation = useMutation({
    mutationFn: (data: FormData) =>
      AuthServices.completePasswordReset({
        email,
        password: data.password,
      }),
    onSuccess: (data) => {
      const access_token = data?.data?.access_token || "";
      const refresh_token = data?.data?.refresh_token;
      setAuthData(
        access_token,
        {
          email,
          first_name: "",
          last_name: "",
          id: "",
          profile_picture: "",
          password_set: false,
          transaction_pin_set: false,
        },
        refresh_token
      );
      fourthStep();
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });

  const handleSubmit = () => {
    const isValidPassword = validatePassword(newPassword);
    if (!passwordError && isValidPassword && newPassword === confirmPassword) {
      mutation.mutate({ password: newPassword });
    }
  };

  const isFormValid =
    newPassword &&
    confirmPassword &&
    !passwordError &&
    Object.values(passwordValidation).every(Boolean);

  return (
    <div className="mb-0 px-[24px]">
      {/* Progress indicator */}
      <div className="flex gap-0 mb-6 w-[81px] bg-gray-200 rounded-full">
        <div className="h-[8px] bg-primary-750 rounded-full w-full"></div>
      </div>

      {/* Header */}
      <h3 className="tracking-[0.12em] text-sm text-grey-250 mb-2">
        YOU ARE SO BACK!!
      </h3>
      <p className="font-semibold text-[32px] mb-4 leading-[96%]">
        Secure your Account
      </p>
      <p className="text-grey-250 mb-8 text-base">
        Please enter in a new password. Ensure not <br /> to forget this
        password this time.
      </p>

      <div className="flex flex-col h-[357.21px] grow-[1] justify-between">
        <div>
          <div className="mb-4">
            <label className="block text-sm font-medium text-grey-500 mb-2">
              New Password
            </label>
            <input
              type="password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              placeholder="Enter new password"
              className="w-full h-[56px] px-4 border border-stroke-gray-300 rounded-full shadow-xs shadow-[hsla(220,29%,5%,0.05)] focus:outline-none focus:border-[#A6AAF9]"
            />

            {/* Password Requirements */}
            {newPassword && (
              <div className="mt-3 space-y-1">
                <p className="text-xs text-gray-600 mb-2">
                  Password must contain:
                </p>
                <div className="grid grid-cols-1 gap-1 text-xs">
                  <div
                    className={`flex items-center gap-2 ${
                      passwordValidation.length
                        ? "text-green-600"
                        : "text-gray-500"
                    }`}
                  >
                    <span
                      className={`w-2 h-2 rounded-full ${
                        passwordValidation.length
                          ? "bg-green-500"
                          : "bg-gray-300"
                      }`}
                    ></span>
                    At least 8 characters
                  </div>
                  <div
                    className={`flex items-center gap-2 ${
                      passwordValidation.uppercase
                        ? "text-green-600"
                        : "text-gray-500"
                    }`}
                  >
                    <span
                      className={`w-2 h-2 rounded-full ${
                        passwordValidation.uppercase
                          ? "bg-green-500"
                          : "bg-gray-300"
                      }`}
                    ></span>
                    One uppercase letter (A-Z)
                  </div>
                  <div
                    className={`flex items-center gap-2 ${
                      passwordValidation.lowercase
                        ? "text-green-600"
                        : "text-gray-500"
                    }`}
                  >
                    <span
                      className={`w-2 h-2 rounded-full ${
                        passwordValidation.lowercase
                          ? "bg-green-500"
                          : "bg-gray-300"
                      }`}
                    ></span>
                    One lowercase letter (a-z)
                  </div>
                  <div
                    className={`flex items-center gap-2 ${
                      passwordValidation.number
                        ? "text-green-600"
                        : "text-gray-500"
                    }`}
                  >
                    <span
                      className={`w-2 h-2 rounded-full ${
                        passwordValidation.number
                          ? "bg-green-500"
                          : "bg-gray-300"
                      }`}
                    ></span>
                    One number (0-9)
                  </div>
                  <div
                    className={`flex items-center gap-2 ${
                      passwordValidation.special
                        ? "text-green-600"
                        : "text-gray-500"
                    }`}
                  >
                    <span
                      className={`w-2 h-2 rounded-full ${
                        passwordValidation.special
                          ? "bg-green-500"
                          : "bg-gray-300"
                      }`}
                    ></span>
                    One special character (!@#$%^&*)
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-grey-500 mb-2">
              Confirm Password
            </label>
            <input
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="Re-enter new password"
              className="w-full h-[56px] px-4 border border-stroke-gray-300 rounded-full shadow-xs shadow-[hsla(220,29%,5%,0.05)] focus:outline-none focus:border-[#A6AAF9]"
            />
          </div>

          {passwordError && (
            <p className="text-red-500 text-sm mb-4">{passwordError}</p>
          )}
        </div>
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={!isFormValid || mutation.isPending}
          variant="primary"
          isLoading={mutation.isPending}
        >
          Setup Password
        </Button>
      </div>
    </div>
  );
};
