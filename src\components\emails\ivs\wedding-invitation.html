<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Wedding Invitation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Albert+Sans:wght@500;800&family=Comforter:wght@400&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Albert Sans", sans-serif;
        background-color: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .invitation-container {
        position: relative;
        width: 1240px;
        height: 1748px;
        background-image: url("https://customer-preprod.eventpark.africa/assets/email-templates/wedding-invitation-background.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        max-width: 100%;
        max-height: 100vh;
        object-fit: contain;
      }

      .text-element {
        position: absolute;
      }

      .wedding-header {
        font-family: "Albert Sans", sans-serif;
        font-weight: 500;
        font-size: 35px;
        line-height: 1.2em;
        letter-spacing: 5%;
        color: #3a3a3a;
        left: 418px;
        top: 188px;
        width: 404px;
        height: 42px;
      }

      .first-celebrant {
        font-family: "Comforter", cursive;
        font-weight: 400;
        font-size: 170px;
        line-height: 1.4099999820484834em;
        letter-spacing: 5%;
        color: #5b8ca6;
        left: 213px;
        top: 362px;
        width: 814px;
        height: 240px;
        text-align: center;
        transform-origin: left center;
      }

      .connector-and {
        font-family: "Comforter", cursive;
        font-weight: 400;
        font-size: 75px;
        line-height: 1.41em;
        letter-spacing: 5%;
        color: #5b8ca6;
        left: 570px;
        top: 602px;
        width: 101px;
        height: 106px;
      }

      .second-celebrant {
        font-family: "Comforter", cursive;
        font-weight: 400;
        font-size: 170px;
        line-height: 1.4099999820484834em;
        letter-spacing: 5%;
        color: #5b8ca6;
        left: 227px;
        top: 752px;
        width: 787px;
        height: 240px;
        transform-origin: left center;
      }

      .honour-text {
        font-family: "Albert Sans", sans-serif;
        font-weight: 500;
        font-size: 32px;
        line-height: 1.2000000476837158em;
        letter-spacing: 5%;
        text-align: center;
        color: #595959;
        left: 251px;
        top: 1044px;
        width: 739px;
        height: 76px;
      }

      .save-date-header {
        font-family: "Albert Sans", sans-serif;
        font-weight: 500;
        font-size: 34px;
        line-height: 1.2000000897575827em;
        letter-spacing: 5%;
        text-align: center;
        color: #595959;
        left: 251px;
        top: 1206px;
        width: 739px;
        height: 41px;
      }

      .date-day {
        font-family: "Albert Sans", sans-serif;
        font-weight: 800;
        font-size: 36px;
        line-height: 1.2000000211927626em;
        letter-spacing: 5%;
        text-align: center;
        color: #5b8ca6;
        left: 342px;
        top: 1334px;
        width: 163px;
        height: 43px;
      }

      .date-main {
        font-family: "Albert Sans", sans-serif;
        font-weight: 800;
        font-size: 36px;
        line-height: 1.2000000211927626em;
        letter-spacing: 5%;
        text-align: center;
        color: #5b8ca6;
        left: 542px;
        top: 1209px;
        width: 157px;
        height: 86px;
        white-space: pre-line;
      }

      .date-time {
        font-family: "Albert Sans", sans-serif;
        font-weight: 800;
        font-size: 36px;
        line-height: 1.2000000211927626em;
        letter-spacing: 5%;
        text-align: center;
        color: #5b8ca6;
        left: 735px;
        top: 1334px;
        width: 176px;
        height: 43px;
      }

      .venue-header {
        font-family: "Albert Sans", sans-serif;
        font-weight: 500;
        font-size: 34px;
        line-height: 1.2000000897575827em;
        letter-spacing: 5%;
        text-align: center;
        color: #595959;
        left: 251px;
        top: 1463px;
        width: 739px;
        height: 41px;
      }

      .venue-address {
        font-family: "Albert Sans", sans-serif;
        font-weight: 500;
        font-size: 32px;
        line-height: 1.1875em;
        letter-spacing: 5%;
        text-align: center;
        color: #595959;
        left: 251px;
        top: 1515px;
        width: 739px;
        height: 76px;
      }

      .decorative-line-1 {
        position: absolute;
        left: 529px;
        top: 1299px;
        width: 0px;
        height: 98px;
        border-left: 3px solid #5b8ca6;
      }

      .decorative-line-2 {
        position: absolute;
        left: 711px;
        top: 1299px;
        width: 0px;
        height: 98px;
        border-left: 3px solid #5b8ca6;
      }

      /* Dynamic scaling for celebrant names */
      /* Base: 15 chars = scale(1.0), scaling down as needed */

      .first-celebrant[data-length="16"] {
        transform: scale(0.95);
      }
      .first-celebrant[data-length="17"] {
        transform: scale(0.9);
      }
      .first-celebrant[data-length="18"] {
        transform: scale(0.85);
      }
      .first-celebrant[data-length="19"] {
        transform: scale(0.8);
      }
      .first-celebrant[data-length="20"] {
        transform: scale(0.75);
      }
      .first-celebrant[data-length="21"] {
        transform: scale(0.7);
      }
      .first-celebrant[data-length="22"] {
        transform: scale(0.65);
      }
      .first-celebrant[data-length="23"] {
        transform: scale(0.6);
      }
      .first-celebrant[data-length="24"] {
        transform: scale(0.55);
      }
      .first-celebrant[data-length="25"] {
        transform: scale(0.5);
      }
      .first-celebrant[data-length="26"],
      .first-celebrant[data-length="27"],
      .first-celebrant[data-length="28"],
      .first-celebrant[data-length="29"],
      .first-celebrant[data-length="30"] {
        transform: scale(0.45);
      }

      .second-celebrant[data-length="16"] {
        transform: scale(0.95);
      }
      .second-celebrant[data-length="17"] {
        transform: scale(0.9);
      }
      .second-celebrant[data-length="18"] {
        transform: scale(0.85);
      }
      .second-celebrant[data-length="19"] {
        transform: scale(0.8);
      }
      .second-celebrant[data-length="20"] {
        transform: scale(0.75);
      }
      .second-celebrant[data-length="21"] {
        transform: scale(0.7);
      }
      .second-celebrant[data-length="22"] {
        transform: scale(0.65);
      }
      .second-celebrant[data-length="23"] {
        transform: scale(0.6);
      }
      .second-celebrant[data-length="24"] {
        transform: scale(0.55);
      }
      .second-celebrant[data-length="25"] {
        transform: scale(0.5);
      }
      .second-celebrant[data-length="26"],
      .second-celebrant[data-length="27"],
      .second-celebrant[data-length="28"],
      .second-celebrant[data-length="29"],
      .second-celebrant[data-length="30"] {
        transform: scale(0.45);
      }

      @media screen and (max-width: 1280px) {
        .invitation-container {
          transform: scale(0.8);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 1024px) {
        .invitation-container {
          transform: scale(0.6);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 768px) {
        .invitation-container {
          transform: scale(0.4);
          transform-origin: center center;
        }
      }
    </style>
  </head>
  <body>
    <div class="invitation-container">
      <div class="text-element wedding-header">WEDDING INVITATION</div>

      <div class="text-element first-celebrant">
        tobi
        <!-- {{#if two_celebrant}} {{{first_celebrant}}} {{else}}
        {{{celebrant_name}}} {{/if}} -->
      </div>

      <!-- {{#if two_celebrant}}
      <div class="text-element connector-and">And</div>

      <div class="text-element second-celebrant">{{{second_celebrant}}}</div>
      {{/if}} -->

      <div class="text-element honour-text">{{{honour_text}}}</div>

      <div class="text-element save-date-header">SAVE THE DATE</div>

      <div class="text-element date-day">
        <!-- {{{event_day}}} -->
        saturday
      </div>

      <div class="text-element date-main">
        <!-- {{{event_date}}} -->
        03 JULY 2026
      </div>

      <div class="text-element date-time">
        <!-- {{{event_time}}} -->
        dhjdjdjd
      </div>

      <div class="decorative-line-1"></div>
      <div class="decorative-line-2"></div>

      <div class="text-element venue-header">VENUE</div>

      <div class="text-element venue-address">{{{venue_address}}}</div>
    </div>

    <script>
      function calculateCelebrantNamesLength() {
        const firstCelebrant = document.querySelector(".first-celebrant");
        const secondCelebrant = document.querySelector(".second-celebrant");

        if (firstCelebrant) {
          const textContent =
            firstCelebrant.textContent || firstCelebrant.innerText;
          const cleanText = textContent.replace(/\s+/g, " ").trim();
          const totalLength = cleanText.length;
          firstCelebrant.setAttribute("data-length", totalLength.toString());

          console.log(`First celebrant length: ${totalLength} characters`);
        }

        if (secondCelebrant) {
          const textContent =
            secondCelebrant.textContent || secondCelebrant.innerText;
          const cleanText = textContent.replace(/\s+/g, " ").trim();
          const totalLength = cleanText.length;
          secondCelebrant.setAttribute("data-length", totalLength.toString());

          console.log(`Second celebrant length: ${totalLength} characters`);
        }
      }

      // Run the calculation when the page loads
      document.addEventListener(
        "DOMContentLoaded",
        calculateCelebrantNamesLength
      );

      // Also run it after a short delay to ensure all content is loaded
      setTimeout(calculateCelebrantNamesLength, 100);
    </script>
  </body>
</html>
