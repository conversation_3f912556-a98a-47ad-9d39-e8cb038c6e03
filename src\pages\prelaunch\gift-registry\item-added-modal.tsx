import React from "react";
import giftBox from "../../../assets/images/gift-box.png";

interface ItemAddedModalProps {
  itemName: string;
  onProceedToCashGift: () => void;
  onContinueAddingItems: () => void;
}

export const ItemAddedModal: React.FC<ItemAddedModalProps> = ({
  itemName,
  onProceedToCashGift,
  onContinueAddingItems,
}) => {
  return (
    <div className="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl p-6 max-w-md w-full mx-4 shadow-lg">
        <div className="flex flex-col items-center">
          <div className="mb-4">
            <img src={giftBox} alt="Gift box" className="w-32 h-32" />
          </div>

          <h1 className="text-[28px] font-bold text-center mb-1 text-[#000059]">
            Item added
          </h1>
          <p className="text-gray-500 text-center mb-6">successfully</p>

          <p className="text-center mb-6 text-gray-700">
            The item{" "}
            <span className="text-primary-650 font-medium">{itemName}</span> has
            just been added to your gift registry list.
            <br />
            Would you like to proceed?
          </p>

          <div className="flex flex-col sm:flex-row gap-3 w-full">
            <button
              onClick={onProceedToCashGift}
              className="bg-primary-650 text-white py-3 px-4 rounded-full font-medium w-full sm:w-auto flex-1"
            >
              Proceed to Cashgift
            </button>

            <button
              onClick={onContinueAddingItems}
              className="border border-gray-300 text-gray-700 py-3 px-4 rounded-full font-medium w-full sm:w-auto flex-1"
            >
              Continue adding items
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export const SuggestionModal: React.FC<{
  itemName: string;
  suggestedItem: string;
  onProceedToAdd: () => void;
  onAddOtherItems: () => void;
  onClose: () => void;
}> = ({
  itemName,
  suggestedItem,
  onProceedToAdd,
  onAddOtherItems,
  onClose,
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl p-6 max-w-md w-full mx-4 relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 4L4 12M4 4L12 12"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>

        <div className="flex items-center mb-4">
          <img src={giftBox} alt="Gift box" className="w-16 h-16 mr-4" />
          <div>
            <p className="text-gray-700">
              I see you've added{" "}
              <span className="text-primary-650 underline">{itemName}</span> to
              your gift registry
            </p>
          </div>
        </div>

        <h2 className="text-2xl font-bold mb-2">Would you like to add</h2>
        <p className="text-primary-650 text-2xl font-bold underline mb-6">
          {suggestedItem}
        </p>
        <p className="text-gray-500 mb-6">as well?</p>

        <div className="flex flex-col sm:flex-row gap-3 w-full">
          <button
            onClick={onProceedToAdd}
            className="bg-primary-650 text-white py-3 px-4 rounded-full font-medium"
          >
            Yes, Proceed to add
          </button>

          <button
            onClick={onAddOtherItems}
            className="bg-[#FFF2ED] text-gray-700 py-3 px-4 rounded-full font-medium"
          >
            No, Add other items
          </button>
        </div>
      </div>
    </div>
  );
};
